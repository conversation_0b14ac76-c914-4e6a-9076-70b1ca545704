"""
title: Google Gemini Pipe
authors: justinh-rahb and christian-taillon
author_url: https://github.com/justinh-rahb
funding_url: https://github.com/open-webui
version: 0.2.5
required_open_webui_version: 0.3.17
license: MIT
"""

import os
import requests
import json
import time
import html
import asyncio
import uuid
import inspect
import httpx
from typing import (
    AsyncGenerator,
    AsyncIterator,
    Awaitable,
    Callable,
    Any,
    Iterable,
    Literal,
    Mapping,
    NotRequired,
    Optional,
    TypedDict,
    Union,
    Dict,
    List,
)
from pydantic import BaseModel, Field
from open_webui.utils.misc import pop_system_message


class ToolSpecParametersProperty(TypedDict):
    description: str
    type: str
    items: NotRequired[dict[str, str]]
    default: NotRequired[Any]
    enum: NotRequired[list[str]]
    maxItems: NotRequired[int]
    minItems: NotRequired[int]
    prefixItems: NotRequired[list[dict[str, Any]]]


class ToolSpecParameters(TypedDict):
    properties: dict[str, ToolSpecParametersProperty]
    required: NotRequired[list[str]]
    type: str
    additionalProperties: NotRequired[bool]


class ToolSpec(TypedDict):
    name: str
    description: str
    parameters: ToolSpecParameters


class ToolCallable(TypedDict):
    toolkit_id: str
    callable: Callable
    spec: ToolSpec
    pydantic_model: NotRequired[BaseModel]
    file_handler: bool
    citation: bool


class ToolCall(BaseModel):
    id: str
    name: str
    arguments: str


class EventEmitterMessageData(TypedDict):
    content: str


class EventEmitterStatusData(TypedDict):
    description: str
    done: Optional[bool]


class EventEmitterStatus(TypedDict):
    type: Literal["status"]
    data: EventEmitterStatusData


class EventEmitterMessage(TypedDict):
    type: Literal["message"]
    data: EventEmitterMessageData


class Metadata(TypedDict):
    chat_id: str
    user_id: str
    message_id: str


class EventEmitter:
    def __init__(
        self,
        __event_emitter__: Optional[
            Callable[[Mapping[str, Any]], Awaitable[None]]
        ] = None,
    ):
        self.event_emitter = __event_emitter__

    async def emit(
        self, message: Union[EventEmitterMessage, EventEmitterStatus]
    ) -> None:
        if self.event_emitter:
            maybe_future = self.event_emitter(message)
            if asyncio.isfuture(maybe_future) or inspect.isawaitable(maybe_future):
                await maybe_future

    async def status(self, description: str, done: Optional[bool] = None) -> None:
        await self.emit(
            EventEmitterStatus(
                type="status",
                data=EventEmitterStatusData(description=description, done=done),
            )
        )

    async def result(self, summary: str, content: str) -> None:
        await self.emit(
            EventEmitterMessage(
                type="message",
                data=EventEmitterMessageData(
                    content=f'\n<details type="tool_calls" done="true" results="{html.escape(content)}">\n<summary>{summary}</summary>\n{content}\n</details>',
                ),
            )
        )


class ToolCallResult(BaseModel):
    tool_call: ToolCall
    result: Optional[str] = None
    error: Optional[str] = None

    def to_display(self) -> str:
        try:
            # Try to parse the arguments as JSON for better display
            args = json.loads(self.tool_call.arguments)
            args_str = json.dumps(args, indent=2, ensure_ascii=False)
        except json.JSONDecodeError:
            args_str = self.tool_call.arguments

        if self.error:
            return f'\n\n<details type="tool_calls" done="true">\n<summary>Error executing {self.tool_call.name}</summary>\nArguments:\n{args_str}\n\nError: {self.error}\n</details>\n\n'
        
        try:
            # Try to parse the result as JSON for better display
            result_json = json.loads(self.result) if self.result else None
            result_str = json.dumps(result_json, indent=2, ensure_ascii=False) if result_json else "None"
        except json.JSONDecodeError:
            result_str = self.result if self.result else "None"

        # Create a more readable display format
        display = (
            f'\n\n<details type="tool_calls" done="true" results="{html.escape(self.result) if self.result else ""}">'
            f"\n<summary>Executed {self.tool_call.name}</summary>\n"
            f"Arguments:\n{args_str}\n\n"
            f"Result:\n{result_str}\n</details>\n\n"
        )
        print(f"Displaying tool call result for {self.tool_call.name}:")
        print(f"Arguments: {args_str}")
        print(f"Result: {result_str}")
        return display


class ToolCallingChunk(BaseModel):
    message: Optional[str] = None
    tool_calls: Optional[Iterable[ToolCall]] = None


class GeminiToolCallingModel:
    """
    ToolCallingModel for Google Gemini API
    """

    def __init__(self, api_key: str, model_id: str):
        self.api_key = api_key
        self.model_id = model_id
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "content-type": "application/json",
        }
        self.url = "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions"

    async def stream(
        self,
        body: dict,
        __tools__: dict[str, ToolCallable] | None,
    ) -> AsyncIterator[ToolCallingChunk]:
        tools = self._map_tools(__tools__)
        messages = body["messages"]
        
        payload = {
            "model": self.model_id,
            "messages": messages,
            "max_tokens": body.get("max_tokens", 4096),
            "temperature": body.get("temperature", 0.8),
            "top_p": body.get("top_p", 0.9),
            "stream": True,
        }

        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"

        async with httpx.AsyncClient() as client:
            try:
                async with client.stream("POST", self.url, json=payload, headers=self.headers) as response:
                    if response.status_code != 200:
                        try:
                            error_text = await response.aread()
                            error_json = json.loads(error_text.decode())
                            error_message = f"HTTP Error {response.status_code}: {json.dumps(error_json, indent=2)}"
                        except Exception as e:
                            error_message = f"HTTP Error {response.status_code}: Failed to read error response - {str(e)}"
                        print(error_message)
                        yield ToolCallingChunk(message=error_message)
                        return

                    tool_calls_map: dict[str, ToolCall] = {}
                    current_tool_call = None
                    current_json_input = ""
                    message = ""

                    async for line in response.aiter_lines():
                        if not line:
                            continue
                            
                        if line.startswith("data: "):
                            try:
                                data = json.loads(line[6:])
                                print(f"Received data: {json.dumps(data, indent=2)}")
                                
                                if "choices" in data and len(data["choices"]) > 0:
                                    delta = data["choices"][0].get("delta", {})
                                    
                                    if "content" in delta:
                                        content = delta["content"]
                                        if content.strip():
                                            message += content
                                            yield ToolCallingChunk(message=content)
                                    
                                    if "function_call" in delta:
                                        function_call = delta["function_call"]
                                        tool_call_id = str(uuid.uuid4())
                                        
                                        if tool_call_id not in tool_calls_map:
                                            tool_calls_map[tool_call_id] = ToolCall(
                                                id=tool_call_id,
                                                name=function_call.get("name", ""),
                                                arguments="{}"
                                            )
                                            current_json_input = ""
                                        
                                        if "arguments" in function_call:
                                            current_json_input += function_call["arguments"]
                                            try:
                                                json.loads(current_json_input)
                                                tool_calls_map[tool_call_id].arguments = current_json_input
                                                print(f"Valid tool call: {tool_calls_map[tool_call_id].name} with args: {current_json_input}")
                                                yield ToolCallingChunk(tool_calls=[tool_calls_map[tool_call_id]])
                                            except json.JSONDecodeError:
                                                print(f"Incomplete JSON for tool call: {current_json_input}")
                                                pass

                                if data.get("choices", [{}])[0].get("finish_reason"):
                                    if tool_calls_map:
                                        print(f"Final tool calls: {[tc.name for tc in tool_calls_map.values()]}")
                                        yield ToolCallingChunk(tool_calls=tool_calls_map.values())
                                    break

                            except json.JSONDecodeError as e:
                                error_message = f"Failed to parse JSON: {line}"
                                print(error_message)
                                yield ToolCallingChunk(message=error_message)
                            except Exception as e:
                                error_message = f"Error processing stream: {e}"
                                print(error_message)
                                print(f"Line content: {line}")
                                yield ToolCallingChunk(message=error_message)
            except httpx.HTTPError as e:
                error_message = f"HTTP request failed: {str(e)}"
                if hasattr(e, 'response') and e.response is not None:
                    try:
                        error_text = await e.response.aread()
                        error_json = json.loads(error_text.decode())
                        error_message += f"\nResponse: {json.dumps(error_json, indent=2)}"
                    except Exception as read_error:
                        error_message += f"\nFailed to read error response: {str(read_error)}"
                print(error_message)
                yield ToolCallingChunk(message=error_message)
            except Exception as e:
                error_message = f"Unexpected error: {str(e)}"
                print(error_message)
                yield ToolCallingChunk(message=error_message)

    def append_tool_calls(self, body: dict, tool_calls: Iterable[ToolCall]) -> None:
        if "messages" in body:
            tool_call_content = []
            for tool_call in tool_calls:
                try:
                    args = json.loads(tool_call.arguments)
                    print(f"Appending tool call {tool_call.name} with args: {args}")
                    tool_call_content.append({
                        "role": "assistant",
                        "content": None,
                        "function_call": {
                            "name": tool_call.name,
                            "arguments": json.dumps(args)
                        }
                    })
                except json.JSONDecodeError as e:
                    print(f"Failed to parse tool call arguments for {tool_call.name}: {tool_call.arguments}")
                    print(f"Error: {e}")
                    continue

            if tool_call_content:
                body["messages"].extend(tool_call_content)

    def append_results(self, body: dict, results: Iterable[ToolCallResult]) -> None:
        if "messages" in body:
            for result in results:
                content = result.error if result.error else result.result
                if not content:
                    print(f"Empty content for tool result: {result.tool_call.name}")
                    continue

                try:
                    parsed_content = json.loads(content)
                    tool_result = {
                        "role": "function",
                        "name": result.tool_call.name,
                        "content": json.dumps(parsed_content, ensure_ascii=False)
                    }
                except json.JSONDecodeError:
                    tool_result = {
                        "role": "function",
                        "name": result.tool_call.name,
                        "content": content
                    }
                
                body["messages"].append(tool_result)
                print(f"Added tool result message to conversation history")

    def append_assistant_message(self, body: dict, message: str) -> None:
        if "messages" in body:
            body["messages"].append(
                {
                    "role": "assistant",
                    "content": message
                }
            )

    def _map_tools(
        self, tool_specs: dict[str, ToolCallable] | None
    ) -> list[dict]:
        gemini_tools = []
        for tool in tool_specs.values() if tool_specs else []:
            tool_spec = {
                "type": "function",
                "function": {
                    "name": tool["spec"]["name"],
                    "description": tool["spec"].get("description", ""),
                    "parameters": {
                        "type": "object",
                        "properties": tool["spec"].get("parameters", {}).get("properties", {}),
                        "required": tool["spec"].get("parameters", {}).get("required", [])
                    }
                }
            }
            gemini_tools.append(tool_spec)
        return gemini_tools


class Pipe:
    class Valves(BaseModel):
        GOOGLE_API_KEY: str = Field(default="")

    def __init__(self):
        self.type = "manifold"
        self.id = "gemini"
        self.name = "gemini/"
        self.valves = self.Valves(
            **{"GOOGLE_API_KEY": os.getenv("GOOGLE_API_KEY", "")}
        )
        self.MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB per image

    def get_gemini_models(self):
        return [
            {"id": "gemini-2.5-flash-preview-04-17", "name": "gemini-2.5-flash"},
            {"id": "gemini-2.5-pro-preview-03-25", "name": "gemini-2.5-pro"},
        ]

    def pipes(self) -> List[dict]:
        return self.get_gemini_models()

    async def execute_tool(
        self,
        tool_call: ToolCall,
        tools: dict[str, ToolCallable],
        ev: EventEmitter,
    ) -> ToolCallResult:
        try:
            tool = tools.get(tool_call.name)
            if not tool:
                raise ValueError(f"Tool '{tool_call.name}' not found")

            if tool_call.arguments:
                parsed_args = json.loads(tool_call.arguments)
                await ev.status(
                    f"Executing tool '{tool_call.name}' with arguments: {parsed_args}"
                )
            else:
                parsed_args = {}

            result = await tool["callable"](**parsed_args)

            return ToolCallResult(
                tool_call=tool_call,
                result=json.dumps(result),
            )
        except json.JSONDecodeError:
            return ToolCallResult(
                tool_call=tool_call,
                error=f"Failed to parse arguments for tool '{tool_call.name}'",
            )
        except Exception as e:
            return ToolCallResult(
                tool_call=tool_call,
                error=f"Error executing tool '{tool_call.name}': {str(e)}",
            )

    async def pipe(
        self,
        body: dict,
        __metadata__: Metadata,
        __user__: dict | None = None,
        __task__: str | None = None,
        __tools__: dict[str, ToolCallable] | None = None,
        __event_emitter__: Callable[[Mapping[str, Any]], Awaitable[None]] | None = None,
    ) -> AsyncGenerator[str, None]:
        if __task__ == "function_calling":
            return

        model_id = body["model"][body["model"].find(".") + 1 :]
        model = GeminiToolCallingModel(self.valves.GOOGLE_API_KEY, model_id)
        ev = EventEmitter(__event_emitter__)

        try:
            while True:
                await ev.status("Generating response...")
                tool_calls: list[ToolCall] = []

                # Stream model response: pass text content through and collect tool calls
                message = ""
                try:
                    async for chunk in model.stream(body, __tools__):
                        if chunk.tool_calls:
                            tool_calls = list(chunk.tool_calls)
                        if chunk.message and chunk.message.strip():
                            message += chunk.message
                            yield chunk.message
                except Exception as e:
                    error_message = f"Error during model streaming: {str(e)}"
                    print(error_message)
                    yield error_message
                    return

                if message.strip():
                    model.append_assistant_message(body, message)

                if not tool_calls:
                    # No tools to execute, stop the loop
                    await ev.status("Done", done=True)
                    break

                if not __tools__:
                    raise ValueError("No tools provided while tool call was requested")

                model.append_tool_calls(body, tool_calls)

                # Execute tools and process results
                await ev.status("Executing tools...")
                tool_call_results = []
                for tool_call in tool_calls:
                    try:
                        # Display tool call with its arguments
                        try:
                            args = json.loads(tool_call.arguments)
                            args_str = json.dumps(args, indent=2, ensure_ascii=False)
                        except json.JSONDecodeError:
                            args_str = tool_call.arguments
                            
                        tool_info = f"\n\n<details type='tool_calls'>\n<summary>Tool Call: {tool_call.name}</summary>\nArguments:\n{args_str}\n"
                        
                        result = await self.execute_tool(
                            tool_call,
                            __tools__,
                            ev,
                        )
                        
                        if result.result or result.error:
                            tool_call_results.append(result)
                            # Add the result to the same details block
                            if result.error:
                                tool_info += f"\nError: {result.error}\n"
                            else:
                                try:
                                    result_json = json.loads(result.result)
                                    result_str = json.dumps(result_json, indent=2, ensure_ascii=False)
                                except json.JSONDecodeError:
                                    result_str = result.result if result.result else "None"
                                tool_info += f"\nResult:\n{result_str}\n"
                            
                            tool_info += "</details>\n\n"
                            yield tool_info
                    except Exception as e:
                        error_message = f"Error executing tool {tool_call.name}: {str(e)}"
                        print(error_message)
                        error_result = ToolCallResult(
                            tool_call=tool_call,
                            error=error_message
                        )
                        tool_call_results.append(error_result)
                        yield f"\n\n<details type='tool_calls'>\n<summary>Tool Call: {tool_call.name}</summary>\nArguments:\n{args_str}\n\nError: {error_message}\n</details>\n\n"

                if tool_call_results:
                    try:
                        # Add to body for next iteration(s)
                        model.append_results(body, tool_call_results)
                    except Exception as e:
                        error_message = f"Error processing tool results: {str(e)}"
                        print(error_message)
                        yield error_message

                tool_calls = []
                await ev.status("Tool execution complete", done=True)

        except Exception as e:
            error_message = f"Unexpected error in pipe: {str(e)}"
            print(error_message)
            yield error_message
            return
