# Agent SNCF

Un agent spécialisé pour les informations de trains SNCF utilisant le protocole MCP, basé sur [Google ADK](https://google.github.io/adk-docs/).

## Vue d'ensemble

Ce dossier contient un exemple d'utilisation de google-adk pour avoir un ensemble d'agents intéragisant entre eux. Il n'est pas destiné à être utilisé en l'état, mais est surtout là pour montrer ce qu'il est possible de faire.

## Installation

```bash
cd agent
uv sync
```

## Configuration

### Variables d'environnement

Copiez le fichier `.env.example` vers `.env` et configurez vos clés API :

```bash
cp .env.example .env
```

Éditez le fichier `.env` avec vos clés API :

```bash
# Clés API LLM
OPENAI_API_KEY=votre_cle_api_openai
ANTHROPIC_API_KEY=votre_cle_api_anthropic

# Pour Vertex AI avec compte de service
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=votre-id-projet
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS="./credentials.json"
```

**Ressources utiles pour la configuration :**
- [Guide de configuration des clés API LiteLLM](https://docs.litellm.ai/docs/set_keys)
- [Configuration Google ADK](https://docs.litellm.ai/docs/tutorials/google_adk)

## Utilisation

### Lancement de l'agent

```bash
cd agent
uv run adk web
```