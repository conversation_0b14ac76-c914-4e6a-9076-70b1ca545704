import datetime
from google.adk.agents import LlmAgent, ParallelAgent, SequentialAgent
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.sessions import InMemorySessionService
from google.adk.artifacts import InMemoryArtifactService

tools = [MCPToolset(
        connection_params=StdioServerParameters(
            command='uv',
            args=["--directory", "../mcp_server", "run", "main.py"],
        )
    )
]

artifact_service = InMemoryArtifactService()
session_service = InMemorySessionService()

geminiAgent = LlmAgent(
    model='gemini-2.5-pro-preview-05-06',
    name='sncf_assistant_gemni',
    instruction=(
        f"You are a helpful assistant specialized in SNCF train information. Current datetime: {datetime.datetime.now()}. Use the tools to get the right information."
    ),
    tools=tools,
    output_key='geminiResult'
)

anthropicAgent = LlmAgent(
    model=LiteLlm('anthropic/claude-3-5-sonnet-latest'),
    name='sncf_assistant_claude',
    instruction=(
        f"You are a helpful assistant specialized in SNCF train information. Current datetime: {datetime.datetime.now()}. Use the tools to get the right information."
    ),
    tools=tools,
    output_key='claudeResult'
)

openaiAgent = LlmAgent(
    model=LiteLlm('openai/gpt-4o'),
    name='sncf_assistant_openai',
    instruction=(
        f"You are a helpful assistant specialized in SNCF train information. Current datetime: {datetime.datetime.now()}. Use the tools to get the right information."
    ),
    tools=tools,
    output_key='openaiResult'
)

parallelAgent = ParallelAgent(
    sub_agents=[geminiAgent, anthropicAgent, openaiAgent],
    name='sncf_assistant_parallel',
    description="This is a parallel agent that runs multiple LLMs to compare their results.",
)

mergerAgent = LlmAgent(
    model='gemini-2.5-pro-preview-05-06',
    name='sncf_assistant_merger',
    instruction=(
        """ You are an AI assistant responsible of comparing the results of multiple LLMs.
        You should compare the results of the LLMs, and return all of them with you thoughts.
        
        **Input Summaries:**
        
        *   **Gemini:** 
            {geminiResult}
        
        *   **Claude:** 
            {claudeResult}
        
        *   **OpenAI:** 
            {openaiResult}

        ** Output Format:**
        
        ## LLM Results

        ### Gemini output
        (Based on Gemini findings)
        [Gemini output only]

        ### Claude output
        (Based on Claude findings)
        [Claude output only]

        ### OpenAI output
        (Based on OpenAI findings)
        [OpenAI output only]

        ## Comparisong
        (Your thoughts on the results)
        [Your thoughts on the results, should compare the results of all 3 models]
        
        """
    ),
)

root_agent = SequentialAgent(
    sub_agents=[parallelAgent, mergerAgent],
    name='sncf_assistant_pipeline',
    description="This is a pipeline agent that runs multiple LLMs to compare their results.",
)