# Client Central

Un paquet client Multi-LLM propre et maintenable pour l'appel d'outils MCP utilisant [Google ADK](https://google.github.io/adk-docs/) et [LiteLLM](https://docs.litellm.ai/).

## Fonctionnalités

- **Support Multi-LLM** : Interagir simultanément avec plusieurs fournisseurs LLM (OpenAI, Anthropic, Google Gemini)
- **Appel d'outils MCP** : Support complet du protocole MCP pour l'appel d'outils sur tous les fournisseurs
- **Intégration Google ADK** : Gestion unifiée de tous les fournisseurs via Google ADK
- **Cœur agnostique à l'interface** : Séparation claire entre fonctionnalité centrale et interface utilisateur
- **Journalisation complète** : Journalisation détaillée avec niveaux configurables et rotation de fichiers
- **Support Async/Await** : Support asynchrone complet pour les opérations concurrentes

## Installation

```bash
# Installer le paquet
uv add path/to/core_client
```

## Configuration

### Variables d'environnement

Le Client Central s'attend à ce que les variables d'environnement soient chargées par l'application consommatrice. Un fichier `.env.example` est fourni comme modèle. Les variables d'environnement suivantes sont utilisées :

```bash
# Clés API
OPENAI_API_KEY=votre_cle_api_openai
ANTHROPIC_API_KEY=votre_cle_api_anthropic

# Pour Vertex AI avec compte de service
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=votre-id-projet
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS="./credentials.json"

# Configuration de la journalisation
CORE_CLIENT_LOG_LEVEL=INFO  # Niveau de log global (DEBUG, INFO, WARNING, ERROR, CRITICAL)
CORE_CLIENT_CONSOLE_LOG_LEVEL=INFO  # Niveau de log console
CORE_CLIENT_FILE_LOG_LEVEL=DEBUG  # Niveau de log fichier
# CORE_CLIENT_LOG_FILE=logs/custom_log_file.log  # Chemin de fichier de log personnalisé (optionnel)
```

**Ressources utiles pour la configuration :**
- [Guide de configuration des clés API LiteLLM](https://docs.litellm.ai/docs/set_keys)
- [Configuration Google ADK](https://docs.litellm.ai/docs/tutorials/google_adk)
- [Documentation complète Google ADK](https://google.github.io/adk-docs/)

**Note** : Le Client Central ne charge pas les variables d'environnement ou ne configure pas la journalisation automatiquement. L'application consommatrice doit :

1. Charger les variables d'environnement en utilisant `dotenv` ou une autre méthode
2. Puis importer et utiliser le Client Central

### Configuration du serveur MCP

Configurer les serveurs MCP dans `config.json` :

```json
{
  "defaultServer": "sncf",
  "mcpServers": {
    "sncf": {
      "command": "uv",
      "args": ["--directory", "../mcp_server", "run", "main.py"]
    }
  }
}
```

## Utilisation

### Utilisation de base

```python
import asyncio
from dotenv import load_dotenv
from core_client import MultiLLMClient

async def main():
    # Charger les variables d'environnement en premier
    load_dotenv()

    # Initialiser le client avec des modèles spécifiques
    client = MultiLLMClient(
        models=["anthropic/claude-3-5-sonnet-latest", "openai/gpt-4o"],
        system_prompt="Vous êtes un assistant utile."
    )

    # Se connecter au serveur MCP
    await client.connect_to_server()

    # Traiter une requête
    results = await client.process_query("Quel temps fait-il ?")

    # Nettoyer
    await client.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

### Utilisation avec streaming

```python
import asyncio
from dotenv import load_dotenv
from core_client import MultiLLMClient

async def main():
    # Charger les variables d'environnement en premier
    load_dotenv()

    client = MultiLLMClient()
    await client.connect_to_server()

    # Streamer les tokens avec support d'appel d'outils
    results = await client.stream_tokens(
        "Trouvez les horaires de trains de Paris à Lyon",
        sequential=True  # Traiter les modèles un après l'autre
    )

    await client.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

### Interface en ligne de commande

Pour la fonctionnalité de chat interactif, utilisez le module CLI :

```bash
# Lancer le chat interactif
uv run cli.py --models anthropic/claude-3-5-sonnet-latest openai/gpt-4o
```

Ou utiliser le CLI de manière programmatique :

```python
import asyncio
from mcp_client.cli import chat_loop
from core_client import MultiLLMClient

async def main():
    client = MultiLLMClient()
    await client.connect_to_server()

    # Démarrer la boucle de chat interactive (gérée par CLI)
    await chat_loop(client, use_streaming=True, use_sequential=True)

    await client.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

## Journalisation

Le Client Central inclut une journalisation complète avec les fonctionnalités suivantes :

- **Niveaux de log configurables** : Définir différents niveaux pour la sortie console et fichier
- **Rotation de fichiers** : Rotation automatique des fichiers de log avec limites de taille
- **Journalisation structurée** : Format cohérent avec horodatages, niveaux et informations de source
- **Configuration d'environnement** : Configurer la journalisation via les variables d'environnement

### Niveaux de log

- `DEBUG` : Informations détaillées pour le débogage
- `INFO` : Informations générales sur les opérations
- `WARNING` : Messages d'avertissement pour les problèmes potentiels
- `ERROR` : Messages d'erreur pour les opérations échouées
- `CRITICAL` : Erreurs critiques qui peuvent causer l'arrêt de l'application

### Fichiers de log

Les logs sont automatiquement sauvegardés dans le répertoire `logs/` avec horodatages :

- Format : `core_client_YYYYMMDD_HHMMSS.log`
- Rotation automatique quand les fichiers dépassent 10MB
- Conserve 5 fichiers de sauvegarde par défaut

## Architecture

Le Client Central est organisé en plusieurs modules :

- `multi_llm_client.py` : Interface client principale
- `clients/` : Implémentations de clients spécifiques aux fournisseurs
  - `adk_client.py` : Client basé sur Google ADK pour tous les fournisseurs
  - `base_client.py` : Classe de base abstraite pour les clients
- `config/` : Chargement et validation de configuration
- `utils/` : Fonctions utilitaires incluant journalisation, callbacks et prompts