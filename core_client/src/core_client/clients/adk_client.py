"""
Google ADK client implementation for LLM providers.
"""
import asyncio
from typing import List, Dict, Any, Optional, Callable

from google.genai import types
from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.artifacts.in_memory_artifact_service import InMemoryArtifactService
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

from .base_client import BaseClient
from ..config import load_config, ServerConfig
from ..utils import get_default_system_prompt, get_logger


class ADKClient(BaseClient):
    """
    Client for interacting with multiple LLM providers using Google ADK.

    This client can send the same query to multiple LLM models from different
    providers (OpenAI, Anthropic, Google Gemini) and collect their responses.
    It supports MCP tool calling for all providers through Google ADK.
    """

    def __init__(self, models: Optional[List[str]] = None, system_prompt: Optional[str] = None):
        """
        Initialize the ADK client.

        Args:
            models: List of model IDs to use. If None, uses default models.
                   Format: "provider/model_id" (e.g., "openai/gpt-4o")
            system_prompt: Optional system prompt to guide the models' behavior
        """
        self.logger = get_logger(__name__)
        self.logger.info("Initializing ADKClient")

        # Set up default models if none provided
        self.models = models or [
            "anthropic/claude-sonnet-4-0",
            "openai/o3",
            "vertex_ai/gemini-2.5-pro-preview-05-06"
        ]
        self.logger.debug(f"Using models: {self.models}")

        # Initialize ADK services
        self.session_service = InMemorySessionService()
        self.artifact_service = InMemoryArtifactService()
        self.logger.debug("ADK services initialized")

        # Store configuration
        self.system_prompt = system_prompt or get_default_system_prompt()
        self.logger.debug(f"System prompt length: {len(self.system_prompt)}")

        # These will be initialized when connecting to server
        self.agents = {}
        self.runners = {}
        self.sessions = {}
        self.server_connected = False
        self.mcp_tools = []  # List of MCPToolset instances
        self.mcp_exit_stacks = {}  # Map of server names to exit stacks
        self.server_model_mapping = {}  # Map of model names to server names

        self.logger.info("ADKClient initialized successfully")

    async def connect_to_server(self, config_path: Optional[str] = None, server_names: Optional[List[str]] = None):
        """
        Connect to one or more MCP servers using configuration.

        Args:
            config_path: Path to the configuration file. If None, uses default config.json
            server_names: List of server names to connect to. If None, uses the default server

        Returns:
            List of MCP toolsets connected to the servers
        """
        self.logger.info(f"Connecting to MCP server(s): {server_names or 'default'}")
        self.logger.debug(f"Config path: {config_path}")

        try:
            # Load and validate configuration
            config = load_config(config_path)
            self.logger.debug(f"Configuration loaded successfully")

            # Determine which servers to connect to
            if not server_names:
                # If no servers specified, use the default server
                default_server = config.get("defaultServer")
                if not default_server:
                    # If no default server, use the first server in the config
                    default_server = next(iter(config["mcpServers"]))
                server_names = [default_server]
                self.logger.debug(f"Using default server(s): {server_names}")

            # Connect to each specified server
            for server_name in server_names:
                if server_name not in config["mcpServers"]:
                    error_msg = f"Server '{server_name}' not found in configuration"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                server_config = config["mcpServers"][server_name]
                self.logger.info(f"Connecting to MCP server: {server_name}")
                self.logger.debug(f"Server config: {server_config}")

                # Create MCP toolset - ADK handles the connection and tool discovery
                mcp_toolset = MCPToolset(
                    connection_params=ServerConfig.from_dict(server_config).to_stdio_params()
                )

                # Add the toolset to our list
                self.mcp_tools.append(mcp_toolset)

                # Store the exit stack for cleanup later (managed internally by ADK)
                self.mcp_exit_stacks[server_name] = None
                self.logger.debug(f"Successfully connected to server: {server_name}")

            self.logger.info(f"Connected to {len(self.mcp_tools)} MCP server(s)")

            # Create agents for each model
            for model in self.models:
                await self._create_agent_for_model(model)

            self.server_connected = True
            self.logger.info("All agents created and server connection completed")
            return self.mcp_tools

        except Exception as e:
            self.logger.error(f"Failed to connect to MCP server(s): {str(e)}")
            raise

    async def _create_agent_for_model(self, model_string: str):
        """
        Create an ADK agent for a specific model.

        Args:
            model_string: Full model string in litellm format (e.g., "openai/gpt-4o")
        """
        self.logger.debug(f"Creating agent for model: {model_string}")

        try:
            # Use all available tools by default
            model_tools = self.mcp_tools

            # Create the agent with the appropriate MCP tools
            agent = LlmAgent(
                model=LiteLlm(model_string),
                name='assistant',
                instruction=self.system_prompt,
                tools=model_tools  # ADK handles tool integration
            )
            self.logger.debug(f"Agent created for model: {model_string}")

            # Create a session
            session = await self.session_service.create_session(
                state={},
                app_name='llm_app',
                user_id='user'
            )
            self.logger.debug(f"Session created for model: {model_string}")

            # Create a runner to handle the agent execution
            runner = Runner(
                app_name='llm_app',
                agent=agent,
                artifact_service=self.artifact_service,
                session_service=self.session_service,
            )
            self.logger.debug(f"Runner created for model: {model_string}")

            # Store references for later use
            self.agents[model_string] = agent
            self.runners[model_string] = runner
            self.sessions[model_string] = session

            self.logger.info(f"Successfully created agent for model: {model_string}")

        except Exception as e:
            self.logger.error(f"Failed to create agent for model {model_string}: {str(e)}")
            raise

    async def update_system_prompt(self, system_prompt: str):
        """
        Update the system prompt for all agents.

        Args:
            system_prompt: The new system prompt to use. If empty, uses the default system prompt.
        """
        self.logger.info("Updating system prompt for all agents")
        self.logger.debug(f"New system prompt length: {len(system_prompt) if system_prompt else 0}")

        try:
            if not system_prompt:
                system_prompt = get_default_system_prompt()
                self.logger.debug("Using default system prompt")

            self.system_prompt = system_prompt

            # If agents are already initialized, recreate them with the new system prompt
            if self.server_connected:
                self.logger.debug("Recreating agents with new system prompt")
                for model in self.models:
                    await self._create_agent_for_model(model)
                self.logger.info("All agents updated with new system prompt")
            else:
                self.logger.debug("Agents not yet connected, system prompt will be used when connecting")

        except Exception as e:
            self.logger.error(f"Failed to update system prompt: {str(e)}")
            raise

    async def _process_events(self, events, response_text: str, tool_calls: list, callback=None, streaming=False) -> str:
        """
        Process events from the ADK Runner.

        Args:
            events: The events from the ADK Runner
            response_text: The response text to append to
            tool_calls: The list of tool calls to append to
            callback: Optional callback function for streaming tokens
            streaming: Whether to stream tokens or not

        Returns:
            The accumulated response text
        """
        self.logger.debug(f"Processing events (streaming: {streaming})")

        try:
            async for event in events:
                # Extract text content
                if event.content and event.content.parts:
                    for part in event.content.parts:
                        if hasattr(part, 'text') and part.text:
                            token = part.text
                            response_text += token

                            # Handle streaming if enabled
                            if streaming and callback:
                                await callback(token, False)

                # Process function calls (tool calls)
                function_calls = event.get_function_calls() if hasattr(event, 'get_function_calls') else []
                for call in function_calls:
                    self.logger.debug(f"Processing tool call: {call.name}")
                    tool_call = {
                        "tool_name": call.name,
                        "tool_args": call.args,
                        "result": None
                    }
                    tool_calls.append(tool_call)

                    # Handle streaming tool calls if enabled
                    if streaming and callback:
                        # Notify about tool call
                        await callback("", True, {
                            "tool_name": call.name,
                            "tool_args": call.args,
                            "is_result": False
                        })

                # Process function responses (tool results)
                function_responses = event.get_function_responses() if hasattr(event, 'get_function_responses') else []
                for response in function_responses:
                    self.logger.debug(f"Processing tool result for: {response.name}")
                    # Update the corresponding tool call with the result
                    for tool_call in tool_calls:
                        if tool_call["tool_name"] == response.name and tool_call["result"] is None:
                            tool_call["result"] = response.response

                            # Handle streaming tool results if enabled
                            if streaming and callback:
                                await callback("", True, {
                                    "is_result": True,
                                    "result": response.response
                                })
                            break

        except Exception as e:
            self.logger.error(f"Error processing events: {str(e)}")
            raise

        return response_text

    async def process_query(self, query: str) -> Dict[str, Dict[str, Any]]:
        """
        Process a query using all configured LLMs without streaming.

        Args:
            query: The user query to process

        Returns:
            Dict mapping model IDs to their responses
        """
        self.logger.info("Processing query (non-streaming)")
        self.logger.debug(f"Query length: {len(query)}")

        if not self.server_connected:
            error_msg = "Not connected to an MCP server. Call connect_to_server first."
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            # Format the query as a Content object
            content = types.Content(role='user', parts=[types.Part(text=query)])

            # Process the query with each model concurrently
            tasks = {}
            for model, runner in self.runners.items():
                session = self.sessions[model]
                tasks[model] = asyncio.create_task(self._run_query(runner, session, content, streaming=False))
                self.logger.debug(f"Started task for model: {model}")

            # Wait for all tasks to complete
            results = {}
            for model, task in tasks.items():
                try:
                    results[model] = await task
                    self.logger.debug(f"Completed task for model: {model}")
                except Exception as e:
                    error_msg = f"Error processing query for model {model}: {str(e)}"
                    self.logger.error(error_msg)
                    results[model] = {
                        "error": str(e),
                        "model": model
                    }

            self.logger.info(f"Query processing completed for {len(results)} model(s)")
            return results

        except Exception as e:
            self.logger.error(f"Failed to process query: {str(e)}")
            raise

    async def _run_query(self, runner: Runner, session, content, callback=None, streaming=False) -> Dict[str, Any]:
        """
        Run a query with a specific runner and session, with optional streaming.

        Args:
            runner: The ADK Runner instance
            session: The session to use
            content: The query content
            callback: Optional callback function for streaming tokens
            streaming: Whether to stream tokens or not

        Returns:
            Dict with response and tool call information
        """
        response_text = ""
        tool_calls = []

        # Run the agent and process events
        events = runner.run_async(
            session_id=session.id,
            user_id=session.user_id,
            new_message=content
        )
        response_text = await self._process_events(events, response_text, tool_calls, callback, streaming)

        return {
            "response": response_text,
            "tool_calls": tool_calls,
            "model": runner.agent.name
        }

    async def stream_tokens(self, query: str, callbacks: Dict[str, Callable] = None, sequential: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        Stream tokens from all configured LLMs with tool calling support.
        This implementation streams tokens until a tool call is detected,
        then calls the tool, shows the result, and continues streaming.

        Args:
            query: The user query to process
            callbacks: Dict mapping model IDs to callback functions
                      Function signature: callback(token: str, is_tool_call: bool, tool_info: Optional[Dict] = None)
            sequential: Whether to process models sequentially (one after another) or concurrently

        Returns:
            Dict mapping model IDs to their responses
        """
        if not self.server_connected:
            raise ValueError("Not connected to an MCP server. Call connect_to_server first.")

        callbacks = callbacks or {}
        content = types.Content(role='user', parts=[types.Part(text=query)])
        results = {}

        if sequential:
            # Process models sequentially (one after another)
            for model, runner in self.runners.items():
                session = self.sessions[model]
                callback = callbacks.get(model)

                try:
                    results[model] = await self._run_query(runner, session, content, callback, streaming=True)
                except Exception as e:
                    error_msg = f"Error with {model}: {str(e)}"
                    self.logger.error(error_msg)
                    results[model] = {"error": error_msg, "model": model}
        else:
            # Process models concurrently
            tasks = {
                model: asyncio.create_task(
                    self._run_query(
                        self.runners[model],
                        self.sessions[model],
                        content,
                        callbacks.get(model),
                        streaming=True
                    )
                ) for model in self.runners
            }

            # Wait for all tasks to complete
            for model, task in tasks.items():
                try:
                    results[model] = await task
                except Exception as e:
                    error_msg = f"Error with {model}: {str(e)}"
                    self.logger.error(error_msg)
                    results[model] = {"error": error_msg, "model": model}

        return results

    async def cleanup(self):
        """Clean up resources for all clients."""
        self.logger.info("Cleaning up ADKClient resources")

        try:
            # In the current ADK implementation, the toolsets are automatically cleaned up
            # when they go out of scope, but we'll clear our references to be safe
            self.mcp_tools = []
            self.mcp_exit_stacks = {}
            self.agents = {}
            self.runners = {}
            self.sessions = {}
            self.server_connected = False

            self.logger.info("ADKClient cleanup completed successfully")

        except Exception as e:
            self.logger.error(f"Error during ADKClient cleanup: {str(e)}")
            raise