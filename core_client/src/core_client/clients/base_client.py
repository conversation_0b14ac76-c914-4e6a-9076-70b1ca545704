"""
Base client class for LLM providers.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable


class BaseClient(ABC):
    """
    Abstract base class for LLM clients.

    This class defines the interface that all LLM clients must implement.
    """

    @abstractmethod
    async def connect_to_server(self, config_path: Optional[str] = None, server_names: Optional[List[str]] = None):
        """
        Connect to one or more MCP servers using configuration.

        Args:
            config_path: Path to the configuration file
            server_names: List of server names to connect to

        Returns:
            List of connected MCP toolsets
        """
        pass

    @abstractmethod
    async def update_system_prompt(self, system_prompt: str):
        """
        Update the system prompt for all agents.

        Args:
            system_prompt: The new system prompt to use
        """
        pass

    @abstractmethod
    async def process_query(self, query: str) -> Dict[str, Dict[str, Any]]:
        """
        Process a query using all configured LLMs without streaming.

        Args:
            query: The user query to process

        Returns:
            Dict mapping model IDs to their responses
        """
        pass

    @abstractmethod
    async def stream_tokens(self, query: str, callbacks: Dict[str, Callable] = None, sequential: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        Stream tokens from all configured LLMs with tool calling support.

        Args:
            query: The user query to process
            callbacks: Dict mapping model IDs to callback functions
            sequential: Whether to process models sequentially

        Returns:
            Dict mapping model IDs to their responses
        """
        pass

    @abstractmethod
    async def cleanup(self):
        """Clean up resources for all clients."""
        pass
