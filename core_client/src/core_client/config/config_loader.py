"""
Configuration loading and validation for the Multi-LLM client.
"""
import os
import json
from typing import Dict, Any, Optional

from google.adk.tools.mcp_tool.mcp_toolset import StdioServerParameters
from ..utils import get_logger


class ServerConfig:
    """
    Configuration for an MCP server.
    """

    def __init__(self, command: str, args: list, env: Optional[Dict[str, str]] = None):
        """
        Initialize the server configuration.

        Args:
            command: The command to run the server
            args: The arguments to pass to the command
            env: Optional environment variables to set
        """
        self.command = command
        self.args = args
        self.env = env or {}

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ServerConfig':
        """
        Create a ServerConfig from a dictionary.

        Args:
            config_dict: The configuration dictionary

        Returns:
            A ServerConfig instance
        """
        return cls(
            command=config_dict.get("command", "uv"),
            args=config_dict.get("args", []),
            env=config_dict.get("env", {})
        )

    def to_stdio_params(self) -> StdioServerParameters:
        """
        Convert the server configuration to StdioServerParameters.

        Returns:
            StdioServerParameters instance
        """
        return StdioServerParameters(
            command=self.command,
            args=self.args,
            env=self.env
        )


def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Load the configuration from a file.

    Args:
        config_path: Path to the configuration file. If None, uses default config.json

    Returns:
        The configuration dictionary

    Raises:
        FileNotFoundError: If the configuration file is not found
        ValueError: If the configuration is invalid
    """
    logger = get_logger(__name__)

    # Use default config path if none provided
    if not config_path:
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.json")
        logger.debug(f"Using default config path: {config_path}")
    else:
        logger.debug(f"Using provided config path: {config_path}")

    if not os.path.exists(config_path):
        error_msg = f"Configuration file not found: {config_path}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        logger.debug(f"Configuration loaded successfully from: {config_path}")
    except json.JSONDecodeError as e:
        error_msg = f"Invalid JSON in configuration file {config_path}: {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)
    except Exception as e:
        error_msg = f"Error reading configuration file {config_path}: {str(e)}"
        logger.error(error_msg)
        raise

    # Validate configuration
    if "mcpServers" not in config:
        error_msg = "No MCP servers defined in configuration"
        logger.error(error_msg)
        raise ValueError(error_msg)

    logger.info(f"Configuration loaded with {len(config['mcpServers'])} MCP server(s)")
    return config
