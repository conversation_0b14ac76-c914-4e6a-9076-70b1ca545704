"""
System prompt management for the Multi-LLM client.
"""
import datetime


def get_default_system_prompt() -> str:
    """
    Get the default system prompt with current datetime.
    
    Returns:
        Default system prompt string
    """
    current_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return f"You are a helpful assistant. Current datetime: {current_datetime} (format: YYYY-MM-DD HH:MM:SS). Use the tools to get the right information, do not make up trains that do not exist. Always answer in the language of the question asked (the user)."
