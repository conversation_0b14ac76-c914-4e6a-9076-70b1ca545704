# Client Multi-LLM pour MCP

Un client Python qui peut interagir simultanément avec plusieurs fournisseurs LLM (OpenAI, Anthropic, Google Gemini), en utilisant le protocole MCP pour l'appel d'outils. Basé sur [Google ADK](https://google.github.io/adk-docs/) et [LiteLLM](https://docs.litellm.ai/).

## Fonctionnalités

- Support pour plusieurs fournisseurs LLM :
  - OpenAI (modèles GPT)
  - Anthropic (modèles Claude)
  - Google (modèles Gemini via Vertex AI)
  - Autres fournisseurs supportés par LiteLLM (non testés)
- Support d'appel d'outils MCP pour tous les fournisseurs
- Traitement concurrent de requêtes sur plusieurs modèles
- Mode chat interactif avec plusieurs modèles ou un seul modèle
- Messages système configurables avec inclusion automatique de la date/heure

## Structure du répertoire

```
mcp_client/
├── streamlit_app/           # Application Streamlit complète
│   ├── client_manager.py    # Gestionnaire de clients
│   ├── config.py           # Configuration
│   ├── query_processor.py  # Processeur de requêtes
│   ├── session_state.py    # État de session Streamlit
│   ├── ui_components.py    # Composants d'interface utilisateur
│   └── utils.py            # Utilitaires
├── cli.py                  # Interface en ligne de commande
├── app.py                  # Application Streamlit principale
├── credentials.json        # Identifiants Vertex AI (exemple)
├── .env.example            # Variables d'environnement d'exemple
└── pyproject.toml          # Dépendances du projet
```

## Configuration

### Variables d'environnement

Copiez le fichier `.env.example` vers `.env` et configurez vos clés API :

```bash
cp .env.example .env
```

Éditez le fichier `.env` avec vos clés API :

```bash
# Identifiants API OpenAI
OPENAI_API_KEY=votre_cle_api_openai

# Identifiants API Anthropic
ANTHROPIC_API_KEY=votre_cle_api_anthropic

# Identifiants Google Vertex AI pour les modèles Gemini
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=dty-2025-spring-p2-sncf-mcp
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS="./credentials.json"
```

**Ressources utiles pour la configuration :**
- [Guide de configuration des clés API LiteLLM](https://docs.litellm.ai/docs/set_keys)
- [Configuration Google ADK](https://docs.litellm.ai/docs/tutorials/google_adk)

### Configuration du serveur MCP

Le client utilise un fichier de configuration pour spécifier les paramètres du serveur MCP. Le fichier `config.json` par défaut est automatiquement configuré pour pointer vers le serveur MCP SNCF local, et se trouve dans `core_client/src/core_client/config.json`.

## Utilisation

### Interface en ligne de commande

```bash
# Lancer avec des modèles spécifiques
uv run cli.py --models anthropic/claude-3-5-sonnet-latest openai/gpt-4o

# Lancer avec un seul modèle
uv run cli.py --models anthropic/claude-3-5-sonnet-latest

# Lancer avec un message système personnalisé
uv run cli.py --models anthropic/claude-3-5-sonnet-latest --system-prompt "Vous êtes un assistant spécialisé dans les informations de trains SNCF."

# Utiliser un fichier de configuration spécifique
uv run cli.py --config /chemin/vers/config.json --models anthropic/claude-3-5-sonnet-latest

# Désactiver le streaming
uv run cli.py --models anthropic/claude-3-5-sonnet-latest --no-streaming

# Traitement concurrent (non séquentiel)
uv run cli.py --models anthropic/claude-3-5-sonnet-latest openai/gpt-4o --no-sequential
```

### Interface Streamlit

#### Application Streamlit principale

L'application Streamlit complète fournit une interface utilisateur web pour interagir avec les modèles LLM.

```bash
uv run streamlit run app.py
```

#### Fonctionnalités de l'interface Streamlit

- **Sélection de modèles multiples** - Choisissez n'importe quelle combinaison de modèles de différents fournisseurs
- **Édition du message système** - Modifiez le message système avec le placeholder `{{TIMEDATE}}` pour l'insertion de la date actuelle
- **Rendu HTML** - Rendu approprié du contenu HTML retourné par les outils

### API Python

#### Utilisation programmatique

```python
import asyncio
from core_client import MultiLLMClient

async def main():
    # Initialiser avec des modèles spécifiques
    client = MultiLLMClient(
        models=["anthropic/claude-3-5-sonnet-latest"],
        system_prompt="Vous êtes un assistant spécialisé dans les informations de trains SNCF."
    )

    # Se connecter au serveur MCP
    await client.connect_to_server()

    # Traiter une requête
    result = await client.process_query("Quels sont les prochains trains de Paris à Lyon ?")
    print(result)

    # Nettoyer
    await client.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

#### Streaming avec callback

```python
import asyncio
from core_client import MultiLLMClient

async def token_callback(token: str, is_tool_call: bool, tool_info=None):
    """Callback pour traiter les tokens en streaming."""
    if not is_tool_call:
        print(token, end='', flush=True)
    elif tool_info:
        if tool_info.get('is_result', False):
            print(f"\n[Résultat d'outil] {tool_info['result']}")
        else:
            print(f"\n[Appel d'outil] {tool_info['tool_name']}")

async def main():
    client = MultiLLMClient(
        models=["anthropic/claude-3-5-sonnet-latest"],
        system_prompt="Vous êtes un assistant spécialisé dans les informations de trains SNCF."
    )

    await client.connect_to_server()

    # Streaming avec callback
    callbacks = {"anthropic/claude-3-5-sonnet-latest": token_callback}
    await client.stream_tokens("Quels sont les prochains trains de Paris à Lyon ?", callbacks)

    await client.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
```

## Format des modèles

Les modèles sont spécifiés au format `fournisseur/modele_id`, où :

- `fournisseur` correspond à l'un des fournisseurs (par exemple `anthropic`, `openai`, `vertex_ai`)
- `modele_id` est l'ID spécifique du modèle pour ce fournisseur

Exemples :

- `anthropic/claude-sonnet-4-0` - Modèle Claude d'Anthropic
- `openai/gpt-4o` - Modèle GPT-4o d'OpenAI
- `vertex_ai/gemini-2.5-pro-preview-05-06` - Modèle Gemini Pro de Google

## Commandes disponibles dans l'interface CLI

Une fois dans l'interface de chat interactive, vous pouvez utiliser les commandes suivantes :

- `quit` ou `exit` - Quitter l'application
- `system:<nouveau_message>` - Mettre à jour le message système
- `toggle-streaming` - Activer/désactiver le mode streaming
- `toggle-sequential` - Activer/désactiver le mode séquentiel

## Exemples d'utilisation

### Recherche de gares
```
Quelles sont les gares principales à Paris ?
```

### Horaires de trains
```
Quels sont les prochains trains de Paris Gare de Lyon à Lyon Part-Dieu ?
```

### Planification d'itinéraire
```
Je veux aller de Marseille à Lille demain matin, quelles sont mes options ?
```

### Informations sur un train spécifique
```
Peux-tu me donner des informations sur le TGV 6123 ?
```

## Dépannage

### Problèmes courants

#### Problèmes de clés API
- Assurez-vous que vos clés API sont correctement définies dans le fichier `.env`
- Vérifiez que les clés ne contiennent pas d'espaces ou de caractères supplémentaires
- Pour Vertex AI, vérifiez que votre fichier JSON de compte de service est valide

#### Connexion au serveur MCP
- Vérifiez que la configuration dans le fichier de configuration est correcte
- Consultez les logs pour des messages d'erreur détaillés

### Logs et débogage

Les logs sont automatiquement créés dans le dossier `logs/` du répertoire `core_client/src/core_client/`. Consultez ces fichiers pour des informations détaillées sur les erreurs.


