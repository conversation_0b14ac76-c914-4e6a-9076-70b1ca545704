"""
Command-line interface for the Multi-LLM client.
"""
import asyncio
import argparse
import json
from typing import Any, Callable, Dict, Optional

from dotenv import load_dotenv

from core_client import MultiLLMClient
from core_client.utils import get_logger

async def create_model_callback(model_name: str) -> Callable:
    """
    Create a callback function for a specific model.

    Args:
        model_name: The name of the model

    Returns:
        A callback function for streaming tokens
    """
    logger = get_logger(__name__)
    logger.debug(f"Creating callback for model: {model_name}")

    model_prefix = f"[{model_name}] "
    printed_prefix = False

    async def token_callback(token: str, is_tool_call: bool, tool_info: Optional[Dict[str, Any]] = None):
        nonlocal printed_prefix

        try:
            if is_tool_call and tool_info:
                # Reset prefix state
                printed_prefix = False

                # Handle tool calls and results
                if tool_info.get('is_result', False) and 'result' in tool_info:
                    # Safely convert result to string for logging
                    result = tool_info.get('result', '')
                    result_str = str(result) if result is not None else ''
                    logger.debug(f"Tool result for {model_name}: {result_str[:100]}...")
                    print(f"{model_prefix}[Tool Result] {result_str}")
                else:
                    tool_name = tool_info.get('tool_name', 'unknown')
                    tool_args = tool_info.get('tool_args', {})
                    logger.debug(f"Tool call for {model_name}: {tool_name}")
                    print(f"\n{model_prefix}[Tool Call] {tool_name}({json.dumps(tool_args, indent=2)})")
            elif token.strip():
                # Handle regular text tokens
                if not printed_prefix:
                    print(f"\n{model_prefix}", end='')
                    printed_prefix = True
                print(token, end='', flush=True)

        except Exception as e:
            logger.error(f"Error in token callback for {model_name}: {str(e)}")
            # Don't re-raise to avoid breaking the streaming

    return token_callback


async def chat_loop(client: MultiLLMClient, use_streaming: bool = True, use_sequential: bool = True):
    """
    Run an interactive chat loop with all LLMs.

    Args:
        client: The MultiLLMClient instance
        use_streaming: Whether to use streaming for responses
        use_sequential: Whether to process models sequentially
    """
    logger = get_logger(__name__)
    logger.info(f"Starting chat loop (streaming: {use_streaming}, sequential: {use_sequential})")

    # Print welcome message
    print("\nMulti-LLM Client Started!")
    print(f"Connected models: {', '.join(client.client.models)}")
    print(f"System prompt: {client.client.system_prompt}")
    print("Type your queries or 'quit' to exit. Type 'system:<prompt>' to update the system prompt.")
    print(f"Streaming mode: {'enabled' if use_streaming else 'disabled'}")
    print(f"Sequential mode: {'enabled' if use_sequential else 'disabled'}")
    print("Type 'toggle-streaming' to toggle streaming mode.")
    print("Type 'toggle-sequential' to toggle sequential mode.")

    while True:
        try:
            query = input("\nQuery: ").strip()
            logger.debug(f"User input: {query}")

            # Handle special commands
            if query.lower() in ['quit', 'exit']:
                logger.info("User requested to exit chat loop")
                break
            elif query.startswith('system:'):
                new_system_prompt = query[7:].strip()
                logger.info("User requested system prompt update")
                await client.client.update_system_prompt(new_system_prompt)
                print(f"System prompt updated to: {client.client.system_prompt}")
                continue
            elif query.lower() == 'toggle-streaming':
                use_streaming = not use_streaming
                logger.info(f"Streaming mode toggled to: {use_streaming}")
                print(f"Streaming mode: {'enabled' if use_streaming else 'disabled'}")
                continue
            elif query.lower() == 'toggle-sequential':
                use_sequential = not use_sequential
                logger.info(f"Sequential mode toggled to: {use_sequential}")
                print(f"Sequential mode: {'enabled' if use_sequential else 'disabled'}")
                continue

            # Process the query with or without streaming
            if use_streaming:
                await _handle_streaming_query(client, query, use_sequential)
            else:
                await _handle_non_streaming_query(client, query)

        except Exception as e:
            error_msg = f"Error in chat loop: {str(e)}"
            logger.error(error_msg)
            print(f"\nError: {str(e)}")

    logger.info("Chat loop ended")


async def _handle_streaming_query(client: MultiLLMClient, query: str, use_sequential: bool):
    """
    Handle a query with streaming enabled.

    Args:
        client: The MultiLLMClient instance
        query: The user query
        use_sequential: Whether to process models sequentially
    """
    # Create callbacks for each model
    callbacks = {model: await create_model_callback(model) for model in client.client.models}

    # Stream the responses with tool calling support
    await client.stream_tokens(query, callbacks, sequential=use_sequential)
    print()  # Print a newline after all streaming is done


async def _handle_non_streaming_query(client: MultiLLMClient, query: str):
    """
    Handle a query without streaming.

    Args:
        client: The MultiLLMClient instance
        query: The user query
    """
    # Process without streaming
    results = await client.process_query(query)

    # Display results from each model
    for model, result in results.items():
        print(f"\n--- {model} ---")
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(result["response"])

            if result["tool_calls"]:
                print("\nTool calls:")
                for i, tool_call in enumerate(result["tool_calls"]):
                    print(f"  {i+1}. {tool_call['tool_name']}({json.dumps(tool_call['tool_args'], indent=2)})")
                    print(f"     Result: {str(tool_call['result'])}")


async def main():
    """Main entry point for the Multi-LLM client."""
    # Load environment variables from .env
    load_dotenv()

    parser = argparse.ArgumentParser(description="Multi-LLM Client for MCP tool calling using Google ADK")
    parser.add_argument("--config", help="Path to the configuration file")
    parser.add_argument("--servers", nargs="+", help="Names of the servers to connect to")
    parser.add_argument("--models", nargs="+", help="List of models to use (format: provider/model_id)")
    parser.add_argument("--system-prompt", help="System prompt to guide the models' behavior")
    parser.add_argument("--no-streaming", action="store_true", help="Disable streaming mode")
    parser.add_argument("--no-sequential", action="store_true", help="Disable sequential mode (process models concurrently)")

    args = parser.parse_args()

    # Create client with appropriate configuration
    client = MultiLLMClient(models=args.models, system_prompt=args.system_prompt)

    try:
        # Connect to the specified servers
        await client.connect_to_server(
            config_path=args.config,
            server_names=args.servers
        )

        # Run the chat loop with appropriate settings
        await chat_loop(
            client,
            use_streaming=not args.no_streaming,
            use_sequential=not args.no_sequential
        )
    finally:
        await client.cleanup()


def main_entry():
    """Entry point for the console script."""
    asyncio.run(main())


if __name__ == "__main__":
    main_entry()
