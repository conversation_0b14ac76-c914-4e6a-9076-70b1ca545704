# TravelBot IA - Streamlit Application

Cette application Streamlit fournit une interface utilisateur pour interagir avec les modèles LLM via le protocole MCP, en se concentrant sur les informations des trains SNCF.

## Structure du code

L'application est organisée en modules pour une meilleure maintenabilité :

- `__init__.py` - Définit le package streamlit_app
- `app.py` - Point d'entrée principal de l'application (situé dans le dossier parent)
- `client_manager.py` - Gestion du client MultiLLM (initialisation, connexion, etc.)
- `config.py` - Chargement et gestion de la configuration
- `query_processor.py` - Traitement des requêtes utilisateur
- `session_state.py` - Gestion de l'état de session Streamlit
- `ui_components.py` - Composants d'interface utilisateur
- `utils.py` - Fonctions utilitaires

## Fonctionnalités

- Sélection de modèles multiples (Anthrop<PERSON> Claude, OpenAI GPT, Google Gemini)
- Édition du message système avec insertion de la date/heure
- Streaming de tokens en temps réel
- Visualisation des appels d'outils avec support HTML
- Gestion des serveurs MCP

## Utilisation

Pour lancer l'application :

```bash
cd mcp_client
streamlit run app.py
```

## Dépendances

- streamlit
- nest_asyncio
- python-dotenv
- multi_llm_client (module local)

## Notes de développement

- L'application utilise toujours le mode parallèle pour les requêtes multi-modèles
- Le client est initialisé automatiquement lors de la première requête
- Les réponses HTML des outils sont affichées directement dans l'interface
