"""
Client manager module for the Multi-LLM Client Streamlit application.

This module handles the initialization, connection, and management of the MultiLLMClient
instances used by the application. It ensures a single client instance is maintained
throughout the Streamlit session to preserve memory and conversation history.
"""
import os
import sys
import time
import asyncio
import threading
import streamlit as st
from typing import List, Union

# Add the core_client directory to the path to ensure we can import MultiLLMClient
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'core_client', 'src'))
from core_client import MultiLLMClient

# Global variables for client and event loop
client = None
loop = None
thread = None

# Initialize session state for client tracking
if "client_initialized" not in st.session_state:
    st.session_state.client_initialized = False


async def connect_to_mcp_server(client, server_name=None, config_path=None):
    """
    Connect to the MCP server.

    Args:
        client: The MultiLLMClient instance
        server_name: Optional name of the server to connect to
        config_path: Optional path to the config file

    Returns:
        True if connection was successful, False otherwise
    """
    try:
        # Connect to the server
        await client.connect_to_server(config_path, server_name)
        return True
    except asyncio.TimeoutError:
        print(f"Timeout connecting to server: {server_name}")
        return False
    except Exception as e:
        print(f"Error connecting to server: {str(e)}")
        return False


def reset_client():
    """Reset the client and related variables."""
    global client, loop, thread

    # Stop the event loop if it's running
    if loop is not None:
        loop.call_soon_threadsafe(loop.stop)

    # Reset variables
    client = None
    loop = None
    thread = None

    # Update session state
    st.session_state.client_initialized = False


def initialize_client(models: Union[str, List[str]], system_prompt: str = None) -> bool:
    """
    Initialize the MultiLLMClient with the given models.

    Args:
        models: A single model string or a list of model strings to use
        system_prompt: Optional system prompt to use

    Returns:
        True if initialization was successful, False otherwise
    """
    global client, loop, thread

    # If client is already initialized, just return True
    if client is not None and hasattr(client, 'client') and client.client.server_connected and st.session_state.client_initialized:
        return True

    try:
        # Filter out models with missing API keys
        filtered_models = []
        if isinstance(models, list):
            for model in models:
                provider = model.split("/", 1)[0].lower()
                if provider == "openai" and (not os.environ.get("OPENAI_API_KEY") or os.environ.get("OPENAI_API_KEY") == "your_openai_api_key"):
                    print(f"Skipping {model} due to missing OPENAI_API_KEY")
                    continue
                elif provider == "anthropic" and (not os.environ.get("ANTHROPIC_API_KEY") or os.environ.get("ANTHROPIC_API_KEY") == "your_anthropic_api_key"):
                    print(f"Skipping {model} due to missing ANTHROPIC_API_KEY")
                    continue

                filtered_models.append(model)
        else:
            # Single model case
            provider = models.split("/", 1)[0].lower()
            if provider == "openai" and (not os.environ.get("OPENAI_API_KEY") or os.environ.get("OPENAI_API_KEY") == "your_openai_api_key"):
                print(f"Skipping {models} due to missing OPENAI_API_KEY")
                return False
            elif provider == "anthropic" and (not os.environ.get("ANTHROPIC_API_KEY") or os.environ.get("ANTHROPIC_API_KEY") == "your_anthropic_api_key"):
                print(f"Skipping {models} due to missing ANTHROPIC_API_KEY")
                return False
            filtered_models = [models]

        # If no models are left after filtering, return False
        if not filtered_models:
            print("No models available after filtering out those with missing API keys")
            return False

        # Create a new event loop
        loop = asyncio.new_event_loop()

        # Define a function to run the initialization in the event loop
        def init_client():
            global client
            asyncio.set_event_loop(loop)

            try:
                # Create the client with the filtered models and system prompt
                client = MultiLLMClient(models=filtered_models, system_prompt=system_prompt)

                # Connect to the server
                try:
                    loop.run_until_complete(client.connect_to_server())
                    print("Successfully connected to MCP server")
                except Exception as e:
                    print(f"Error connecting to server: {e}")
                    # Try one more time with a delay
                    time.sleep(2)
                    loop.run_until_complete(client.connect_to_server())
                    print("Successfully connected to MCP server on retry")
            except Exception as e:
                print(f"Error creating client: {e}")
                import traceback
                print(traceback.format_exc())

            # Keep the loop running
            loop.run_forever()

        # Start a thread to run the event loop
        thread = threading.Thread(target=init_client, daemon=True)
        thread.start()

        # Wait for the client to be initialized
        for _ in range(15):  # Wait up to 15 seconds
            if client is not None:
                # Verify that the client is connected to the server
                if hasattr(client, 'client') and client.client.server_connected:
                    # Mark as initialized in session state
                    st.session_state.client_initialized = True
                    return True
                else:
                    # Wait a bit more for the connection to be established
                    time.sleep(1)
            else:
                time.sleep(1)

        # If we get here, the client was created but not connected
        if client is not None and hasattr(client, 'client') and not client.client.server_connected:
            print("Client created but not connected to server, attempting to connect...")
            # Try to connect to the server
            try:
                connect_future = asyncio.run_coroutine_threadsafe(
                    client.connect_to_server(),
                    loop
                )
                connect_future.result(timeout=10)
                st.session_state.client_initialized = True
                return True
            except Exception as e:
                print(f"Error connecting to server: {e}")
                return False

        return False
    except Exception as e:
        print(f"Error initializing client: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False


def get_client():
    """Get the current client instance."""
    global client
    return client


def get_loop():
    """Get the current event loop."""
    global loop
    return loop
