"""
Configuration module for the Multi-LLM Client Streamlit application.

This module handles loading and managing configuration settings for the application,
including MCP server configurations.
"""
import os
import json
from typing import Dict, <PERSON><PERSON>, Optional


def get_available_servers(config_path: str = None) -> Tuple[Dict[str, Dict], Optional[str]]:
    """
    Get available MCP servers from the config file.
    
    Args:
        config_path: Path to the config file. If None, tries to find config.json in the current directory.
        
    Returns:
        Tuple containing:
        - Dictionary of server configurations
        - Default server name or None if not specified
    """
    if config_path is None:
        # Try to find config.json in the current directory
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json")
        if not os.path.exists(config_path):
            print(f"Could not find config file at {config_path}")
            return {}, None

    try:
        with open(config_path, 'r') as f:
            config = json.load(f)

        return config.get("mcpServers", {}), config.get("defaultServer", None)
    except Exception as e:
        print(f"Error loading config file: {str(e)}")
        return {}, None
