"""
Session state management for the Multi-LLM Client Streamlit application.

This module handles the initialization and management of the Streamlit session state
variables used throughout the application.
"""
import streamlit as st


def initialize_session_state():
    """
    Initialize all session state variables used by the application.

    This function ensures that all required session state variables exist
    and are properly initialized with default values.
    """
    # Message history
    if "messages" not in st.session_state:
        st.session_state.messages = []

    # Dictionary to store messages by model
    if "model_messages" not in st.session_state:
        st.session_state.model_messages = {}

    # Server connection tracking
    if "connected_servers" not in st.session_state:
        st.session_state.connected_servers = set()

    # Model response storage
    if "model_responses" not in st.session_state:
        st.session_state.model_responses = {}

    # Processing state flags
    if "processing" not in st.session_state:
        st.session_state.processing = False

    if "processing_id" not in st.session_state:
        st.session_state.processing_id = None

    # Current query tracking
    if "current_query" not in st.session_state:
        st.session_state.current_query = None

    if "current_callbacks" not in st.session_state:
        st.session_state.current_callbacks = None

    # Tool calling test flags
    if "test_tool_calling" not in st.session_state:
        st.session_state.test_tool_calling = False

    if "test_tool_id" not in st.session_state:
        st.session_state.test_tool_id = None

    # Model tracking
    if "last_models" not in st.session_state:
        st.session_state.last_models = []

    # New message flag
    if "new_message" not in st.session_state:
        st.session_state.new_message = False

    # Current prompt
    if "current_prompt" not in st.session_state:
        st.session_state.current_prompt = None

    # Selected tariff
    if "selected_tariff" not in st.session_state:
        st.session_state.selected_tariff = "Tarif Normal"


def add_user_message(prompt: str):
    """
    Add a user message to the session state.

    Args:
        prompt: The user message content
    """
    import datetime

    st.session_state.messages.append({
        "role": "user",
        "content": prompt,
        "is_followup": len(prompt.split()) < 15 or any(x in prompt.lower() for x in ["même", "aussi", "également", "et pour", "et à", "et les"]),
        "timestamp": datetime.datetime.now().isoformat()
    })

    # Set the flag to process the message on the next rerun
    st.session_state.new_message = True
    st.session_state.current_prompt = prompt


def add_assistant_message(model_key: str, response_text: str, html_content=None, tool_calls=None, query=None):
    """
    Add an assistant message to the session state.

    Args:
        model_key: The model identifier
        response_text: The response text content
        html_content: Optional HTML content to display
        tool_calls: Optional list of tool calls made
        query: The query this message is responding to
    """
    import datetime

    st.session_state.messages.append({
        "role": "assistant",
        "model_key": model_key,
        "content": response_text,
        "html_content": html_content,
        "timestamp": datetime.datetime.now().isoformat(),
        "tool_calls": tool_calls or [],
        "query_responded_to": query
    })


def add_error_message(error_text: str):
    """
    Add an error message to the session state.

    Args:
        error_text: The error message content
    """
    st.session_state.messages.append({
        "role": "assistant",
        "content": f"Erreur: {error_text}",
    })


def reset_new_message_flag():
    """Reset the new message flag in the session state."""
    st.session_state.new_message = False
