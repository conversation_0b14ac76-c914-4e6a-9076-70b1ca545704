"""
UI components for the Multi-LLM Client Streamlit application.

This module provides reusable UI components and display functions for the Streamlit interface.
"""
import streamlit as st
from typing import Dict, List, Any, Optional


def display_sidebar():
    """
    Display the sidebar with configuration options.

    Returns:
        Tuple containing:
        - List of selected models
        - System prompt text
    """
    with st.sidebar:
        st.title("TravelBot IA")

        # Model selection
        st.subheader("Sélection du modèle")

        # Model provider checkboxes
        use_anthropic = st.checkbox("Anthropic Claude", value=True)
        use_openai = st.checkbox("OpenAI GPT", value=False)
        use_gemini = st.checkbox("Google Gemini", value=False)

        # Model ID selections based on provider
        selected_models = []

        if use_anthropic:
            anthropic_model = st.selectbox(
                "Claude model:",
                ["claude-3-5-sonnet-20241022", "claude-3-opus-20240229"]
            )
            selected_models.append(f"anthropic/{anthropic_model}")

        if use_openai:
            openai_model = st.selectbox(
                "GPT model:",
                ["gpt-4o", "gpt-4-turbo"]
            )
            selected_models.append(f"openai/{openai_model}")

        if use_gemini:
            gemini_model = st.selectbox(
                "Gemini model:",
                ["gemini-2.5-pro-preview-05-06", "gemini-2.5-flash-preview-04-17"]
            )
            selected_models.append(f"vertex_ai/{gemini_model}")

        # System prompt
        st.subheader("Message système")

        # Default system prompt with current date and time
        import datetime
        current_time = datetime.datetime.now()
        formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
        default_prompt = f"Vous êtes un assistant spécialisé dans les informations sur les trains SNCF. Date et heure actuelles : {formatted_time}"

        # System prompt text area
        system_prompt_text = st.text_area(
            "Message système:",
            default_prompt,
            height=100,
            help="Le message système inclut automatiquement la date et l'heure actuelles."
        )

        # Tariff selection
        st.subheader("Tarification")

        # Tariff options
        tariff_options = [
            "Tarif Normal",
            "Tarif Avantage",
            "Tarif Elève - Etudiant - Apprenti",
            "Tarif Réglementé"
        ]

        selected_tariff = st.selectbox(
            "Sélectionnez votre tarif:",
            tariff_options,
            index=0,  # Default to "Tarif Normal"
            help="Sélectionnez le type de tarif pour afficher les prix correspondants dans les résultats de voyage."
        )

        # No need to show a preview since the date is already included in the prompt

        return selected_models, system_prompt_text, selected_tariff


def display_conversation_history(messages: List[Dict[str, Any]]):
    """
    Display the conversation history with tabs for different model responses.

    Args:
        messages: List of message dictionaries from the session state
    """
    # First, group messages by conversation turn
    conversation_turns = []
    current_turn = {"user": None, "assistant": {}}

    for message in messages:
        if message["role"] == "user":
            # If we already have a complete turn, add it to the list
            if current_turn["user"] is not None:
                conversation_turns.append(current_turn)
                current_turn = {"user": None, "assistant": {}}

            # Set the user message for this turn
            current_turn["user"] = message
        elif message["role"] == "assistant":
            # If this is a model-specific message
            if "model_key" in message:
                model_key = message["model_key"]
                current_turn["assistant"][model_key] = message
            else:
                # Legacy format - add as a generic assistant message
                if "generic" not in current_turn["assistant"]:
                    current_turn["assistant"]["generic"] = []
                current_turn["assistant"]["generic"].append(message)

    # Add the last turn if it's not empty
    if current_turn["user"] is not None:
        conversation_turns.append(current_turn)

    # Display each conversation turn
    for turn in conversation_turns:
        # Display user message
        if turn["user"]:
            with st.chat_message("user"):
                st.markdown(turn["user"]["content"])

        # Display assistant responses
        if turn["assistant"]:
            with st.chat_message("assistant"):
                # Check if we have model-specific responses
                model_responses = {k: v for k, v in turn["assistant"].items() if k != "generic"}

                if model_responses:
                    # Create tabs for each model
                    tab_labels = []
                    for model_key in model_responses.keys():
                        if "/" in model_key:
                            provider, model_id = model_key.split("/", 1)
                            tab_labels.append(f"{provider.capitalize()} ({model_id})")
                        else:
                            tab_labels.append(model_key)

                    tabs = st.tabs(tab_labels)

                    # Fill each tab with the corresponding model's response
                    for i, (model_key, message) in enumerate(model_responses.items()):
                        with tabs[i]:
                            st.markdown(message["content"])

                            # Display HTML content if present
                            if message.get("html_content"):
                                # Ensure the HTML content is displayed with sufficient height
                                # Use a fixed height that's large enough to show the entire table
                                st.components.v1.html(message["html_content"], height=700, scrolling=True)

                # Handle generic assistant messages (legacy format)
                elif "generic" in turn["assistant"]:
                    for message in turn["assistant"]["generic"]:
                        # Regular message
                        st.markdown(message["content"])

                        # Display HTML content if present
                        if message.get("html_content"):
                            # Ensure the HTML content is displayed with sufficient height
                            st.components.v1.html(message["html_content"], height=700, scrolling=True)