# Évaluateur MCP

Un outil d'évaluation pour tester et comparer les performances de différents modèles LLM avec le protocole MCP pour un jeu de données de test.

## Vue d'ensemble

L'évaluateur MCP permet d'évaluer systématiquement les performances de différents modèles LLM en utilisant des jeux de données de test et des métriques de qualité. Il fournit :

- Évaluation automatisée de plusieurs modèles LLM
- Génération de rapports de performance détaillés
- Intégration avec le protocole MCP pour les appels d'outils
- Génération de rapports HTML et JSON

## Installation

```bash
cd mcp_evaluator
uv run prompt_evaluator.py
```

## Configuration

### Variables d'environnement

Copiez le fichier `.env.example` vers `.env` et configurez vos clés API :

```bash
cp .env.example .env
```

Éditez le fichier `.env` avec vos clés API :

```bash
# Clés API
OPENAI_API_KEY=votre_cle_api_openai
ANTHROPIC_API_KEY=votre_cle_api_anthropic

# Pour Vertex AI avec compte de service
GOOGLE_GENAI_USE_VERTEXAI=TRUE
GOOGLE_CLOUD_PROJECT=votre-id-projet
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS="./credentials.json"
```

**Ressources utiles pour la configuration :**
- [Guide de configuration des clés API LiteLLM](https://docs.litellm.ai/docs/set_keys)
- [Configuration Google ADK](https://docs.litellm.ai/docs/tutorials/google_adk)

### Configuration d'évaluation

Modifiez le fichier `config.yml` pour configurer votre évaluation (il faut avoir des clefs API pour chaque modèle appelé):

```yaml
inputs_file: "data/inputs/inputs.json"  # Fichier d'entrées
outputs_folder: "data/outputs/"  # Dossier de sortie pour les résultats
models: # Liste des modèles à évaluer
  - "anthropic/claude-sonnet-4-0"
  - "openai/o3"
  - "vertex_ai/gemini-2.5-pro-preview-05-06"
```

## Structure des données

### Fichier d'entrées

Le fichier `data/inputs/inputs.json` contient une liste de requêtes de test sous la forme :

```json
[
  {
    "prompt": "Quels sont les prochains trains de Paris Gare de Lyon à Lyon Part Dieu demain après 14h ?",
    "expected_tools": [
      {
        "tool_name": "get_journeys",
        "tool_args": {
          "from_station": "Paris Gare de Lyon",
          "to_station": "Lyon Part Dieu"
        }
      }
    ],
    "expected_expected_answer_format": ""
  }
]
```

## Utilisation

### Génération de rapports

Les rapports sont automatiquement générés dans le dossier `data/outputs/` avec :

- Résultats JSON détaillés pour chaque modèle
- Rapport HTML comparatif
- Métriques de performance
- Analyse des appels d'outils

## Métriques d'évaluation

L'évaluateur mesure pour chaque prompt si les bons outils MCP ont été appelés ou non avec les bons arguments.

## Modules

### `prompt_evaluator/`
Évalue la qualité des prompts et des réponses générées.

### `reports_generator/`
Génère des rapports HTML et JSON à partir des résultats d'évaluation.

### `utils/`
Fonctions utilitaires pour le chargement de données, calcul de performances, etc.

## Exemples de rapports

Les rapports générés incluent :

- **Tableau comparatif** des performances par modèle
- **Graphiques** de temps de réponse et taux de succès
- **Analyse détaillée** des appels d'outils

## Dépannage

### Problèmes courants

- **Erreurs de clés API** : Vérifiez que toutes les clés API sont correctement configurées
- **Erreurs de format de données** : Vérifiez la structure du fichier `inputs.json`
