
"""
Module for evaluating prompts with multiple LLM models via MCP Server.
"""
import os
import sys
import json
import asyncio

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.format_response import format_prompt_result
from utils.load_config import load_config
from utils.load_inputs import load_inputs
from utils.compute_models_performance import compute_models_performance
from utils.create_output_dir import create_output_directory
from reports_generator.reports_generator import generate_html_report
from core_client import MultiLLMClient 

from dotenv import load_dotenv
import datetime

load_dotenv()

class PromptEvaluator:
    """
    Class for evaluating prompts with multiple LLM models via MCP Server.
    """
    def __init__(self, config_path:str = None):
        """
        Initialize the prompt evaluator.
        
        Args:
            config_path: Path to the config file
        """
        self.config_path = config_path
        self.config = load_config(config_path)
        self.models = self.config.models
        self.client = MultiLLMClient(models=self.models)
        self.server_connected = False
        self.inputs = load_inputs(self.config.inputs_file)
        self.outputs = None
        self.performance_metrics = None
        self.output_dir = None

    async def evaluate_all_prompts(self):
        """
        Evaluate all prompts with all models and write results to output directory
        """
        # Create timestamped output directory
        self.output_dir = create_output_directory(self.config.outputs_folder)
        print(f"Created output directory: {self.output_dir}")
        
        # Generate timestamp for filenames
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(self.output_dir, f"detailed_results_{timestamp}.json")
        
        # Store all results
        all_results = []
        
        for i, prompt_data in enumerate(self.inputs):
            prompt = prompt_data.get("prompt", "Prompt absent. Ne pas répondre.")
            print(f"Evaluating prompt {i+1}/{len(self.inputs)} : {prompt[:50]}...")
            prompt_result = await self.client.process_query(prompt)
            formatted_prompt_result = format_prompt_result(prompt_data, prompt_result)
            all_results.append(formatted_prompt_result)
            
            # Write current results to file after each prompt
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            print(f"Updated results written to {output_path} after prompt {i+1}/{len(self.inputs)}")
            
            # Add a 1-minute delay between prompts (except after the last one)
            if i < len(self.inputs) - 1:
                print(f"Waiting 2 minutes before next prompt evaluation...")
                await asyncio.sleep(120)
        
        # Store results in evaluator
        self.outputs = all_results
        print(f"All results written to {output_path}")
    
    def compute_performances(self):
        """
        Compute performance metrics for each model based on tool usage.
        
        Evaluates if the tools called by each model match the expected tools
        and if the arguments provided are correct.
        
        Returns:
            Dict containing performance metrics for each model
        """
        if not self.outputs:
            print("No outputs available. Run evaluate_all_prompts first.")
            return None
            
        # Compute performance metrics using the utility function
        self.performance_metrics = compute_models_performance(self.outputs, self.models)
        return self.performance_metrics

    def generate_report(self):
        """
        Generate a report based on the evaluation results.
        """
        if not self.outputs or not self.performance_metrics:
            print("No outputs or performance metrics available. Run evaluate_all_prompts and compute_performances first.")
            return None
        
        if not self.output_dir:
            print("No output directory available. Run evaluate_all_prompts first.")
            return None
        
        # Generate timestamp for filenames
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Generate HTML report
        report_path = os.path.join(self.output_dir, f"evaluation_report_{timestamp}.html")
        
        # Get the template directory
        template_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "reports_generator", 
            "templates"
        )
        
        # Generate the report
        generated_path = generate_html_report(
            performance_metrics=self.performance_metrics,
            outputs=self.outputs,
            output_path=report_path,
            template_dir=template_dir
        )
        
        print(f"Report generated at {generated_path}")
        
        return generated_path


    async def run_prompt_evaluation(self):
        """
        Run the complete prompt evaluation process.
        """
        await self.client.connect_to_server()
        await self.evaluate_all_prompts()
        self.compute_performances()
        self.generate_report()
        await self.client.cleanup()


if __name__ == "__main__":
    CONFIG_PATH = "config.yml"
    evaluator = PromptEvaluator(config_path = CONFIG_PATH)
    asyncio.run(evaluator.run_prompt_evaluation())
