
import os
import datetime
from typing import Dict, List, Any
from jinja2 import Environment, FileSystemLoader

def generate_html_report(
    performance_metrics: Dict[str, Any],
    outputs: List[Dict[str, Any]],
    output_path: str,
    template_dir: str
) -> str:
    """
    Generate an HTML report based on evaluation results.
    
    Args:
        performance_metrics: Performance metrics from compute_models_performance
        outputs: List of prompt evaluation results
        output_path: Path to write the HTML report
        template_dir: Directory containing the Jinja2 templates
        
    Returns:
        Path to the generated report
    """
    # Create Jinja2 environment
    env = Environment(loader=FileSystemLoader(template_dir))
    template = env.get_template("evaluation_report.html.j2")
    
    # Extract models list
    models = list(performance_metrics.keys())
    
    # Prepare data for the template
    model_scores = {}
    model_percentages = {}
    criteria = ["right_tools", "right_args"]  # Criteria from compute_models_performance
    criteria_scores = {}
    criteria_success_rates = {}
    
    # Process model scores
    for model in models:
        model_scores[model] = {
            "total": performance_metrics[model]["overall"]["success_count"],
            "count": performance_metrics[model]["overall"]["total_prompts"]
        }
        model_percentages[model] = f"{performance_metrics[model]['overall']['overall_percentage']:.2f}%"
        
        # Process criteria scores
        criteria_scores[model] = {}
        criteria_success_rates[model] = {}
        
        for criterion in criteria:
            criteria_scores[model][criterion] = {
                "true": performance_metrics[model][criterion]["true"],
                "false": performance_metrics[model]["overall"]["total_prompts"] - performance_metrics[model][criterion]["true"]
            }
            criteria_success_rates[model][criterion] = f"{performance_metrics[model][criterion]['success_rate']:.2f}%"
    
    # Format prompt evaluations for the template
    prompt_evaluations = []
    for i, output in enumerate(outputs):
        prompt_eval = {
            "prompt_number": i + 1,
            "prompt": output["prompt"],
            "model_evaluations": []
        }
        
        for model in models:
            if model in output["model_evaluations"]:
                model_eval = output["model_evaluations"][model]

                # Extract HTML content if available
                html_content = None
                is_complete_html = False
                
                # Check if response contains HTML
                response = model_eval.get("response", "")
                if not response:
                    response = ""
                if "<html" in response.lower() and "</html>" in response.lower():
                    html_content = response
                    is_complete_html = True
                elif "<body" in response.lower() and "</body>" in response.lower():
                    html_content = response
                    is_complete_html = False
                
                # Format tool calls for display
                tool_calls = []
                for tool_call in model_eval.get("tool_calls", []):
                    formatted_tool_call = {
                        "tool_name": tool_call.get("tool_name", ""),
                        "tool_args": tool_call.get("tool_args", {}),
                        "tool_result": tool_call.get("tool_result", "")
                    }
                    tool_calls.append(formatted_tool_call)
                
                # Get criteria results from performance metrics
                criteria_results = {}
                for criterion in criteria:
                    # Find this prompt's performance in the model's prompts_performance list
                    for prompt_perf in performance_metrics[model]["prompts_performance"]:
                        if prompt_perf["prompt"] == output["prompt"]:
                            criteria_results[criterion] = prompt_perf[criterion]
                            break
                
                # Add model evaluation to prompt
                prompt_eval["model_evaluations"].append({
                    "model": model,
                    "response": response,
                    "criteria": criteria_results,
                    "explanation": model_eval.get("explanation", ""),
                    "tools_used_fraction": f"{len(tool_calls)}/{len(output.get('expected_tools', []))}" if output.get("expected_tools") else "",
                    "html_content": html_content,
                    "is_complete_html": is_complete_html,
                    "tool_calls": tool_calls
                })
        
        prompt_evaluations.append(prompt_eval)
    
    # Render the template
    html_content = template.render(
        generation_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        models=models,
        model_scores=model_scores,
        model_percentages=model_percentages,
        criteria=criteria,
        criteria_scores=criteria_scores,
        criteria_success_rates=criteria_success_rates,
        prompt_evaluations=prompt_evaluations
    )
    
    # Write the HTML to file
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    return output_path


if __name__ == "__main__":
    generate_html_report()