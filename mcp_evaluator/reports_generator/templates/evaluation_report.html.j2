<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Rapport d'Évaluation des Modèles</title>
    <style>
        /* Styles pour le rapport d'évaluation */
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
            color: #333;
        }

        table { 
            border-collapse: collapse; 
            width: 100%; 
            margin-bottom: 20px; 
        }

        th, td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left; 
        }

        th { 
            background-color: #f2f2f2; 
        }

        tr:nth-child(even) { 
            background-color: #f9f9f9; 
        }

        h1, h2, h3, h4 { 
            color: #333; 
        }

        .explanation { 
            background-color: #f8f8f8; 
            padding: 10px; 
            border-left: 3px solid #ddd; 
            margin-bottom: 15px; 
        }

        details { 
            background-color: #f9f9f9; 
            padding: 10px; 
            margin-bottom: 15px; 
            border-radius: 5px; 
        }

        details summary { 
            cursor: pointer; 
            font-weight: bold; 
            padding: 5px; 
        }

        details pre { 
            background-color: #f0f0f0; 
            padding: 10px; 
            border-radius: 5px; 
            overflow-x: auto; 
            white-space: pre-wrap; 
            max-height: 400px; 
            overflow-y: auto;
        }

        code { 
            background-color: #f0f0f0; 
            padding: 2px 4px; 
            border-radius: 3px; 
        }

        .tool-call { 
            margin-bottom: 20px; 
            border-left: 3px solid #4CAF50; 
            padding-left: 10px; 
        }

        .tool-result { 
            margin-bottom: 20px; 
            border-left: 3px solid #2196F3; 
            padding-left: 10px; 
        }

        .html-render { 
            border: 1px solid #ddd; 
            padding: 15px; 
            margin: 15px 0; 
            background-color: white; 
        }

        .html-render table { 
            border: 1px solid #333; 
        }

        .html-render th, .html-render td { 
            border: 1px solid #333; 
            padding: 5px; 
        }

        .success { 
            color: #4CAF50; 
            font-weight: bold;
        }

        .failure { 
            color: #F44336; 
            font-weight: bold;
        }

        .summary-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .model-score {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .criteria-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .criteria-item {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .criteria-name {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .criteria-value {
            font-size: 18px;
        }

        .prompt-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .prompt-text {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .model-evaluation {
            margin-bottom: 30px;
            border-left: 4px solid #00a88f;
            padding-left: 15px;
        }

        .criteria-table {
            margin-bottom: 15px;
        }

        .checkmark {
            color: #4CAF50;
            font-size: 18px;
        }

        .xmark {
            color: #F44336;
            font-size: 18px;
        }

        .description {
            color: #666;
            font-style: italic;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Rapport d'Évaluation des Modèles</h1>
    <p><em>Généré le: {{ generation_time }}</em></p>
    
    <h2>Résumé</h2>
    
    <div class="summary-card">
        <h3>Performance Globale</h3>
        <p class="description">Un prompt est considéré comme réussi uniquement si tous les critères sont validés.</p>
        <table>
            <thead>
                <tr>
                    <th>Modèle</th>
                    <th>Prompts réussis</th>
                    <th>Pourcentage</th>
                </tr>
            </thead>
            <tbody>
                {% for model in models %}
                <tr>
                    <td>{{ model }}</td>
                    <td>{{ model_scores[model]["total"] }}/{{ model_scores[model]["count"] }}</td>
                    <td>{{ model_percentages[model] }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="summary-card">
        <h3>Répartition par critères</h3>
        {% for criterion in criteria %}
        <h4>{{ criterion }}</h4>
        <table>
            <thead>
                <tr>
                    <th>Modèle</th>
                    <th>Réussite</th>
                    <th>Taux de Réussite</th>
                </tr>
            </thead>
            <tbody>
                {% for model in models %}
                <tr>
                    <td>{{ model }}</td>
                    <td>{{ criteria_scores[model][criterion]["true"] }} / {{ criteria_scores[model][criterion]["true"] + criteria_scores[model][criterion]["false"] }}</td>
                    <td>{{ criteria_success_rates[model][criterion] }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endfor %}
    </div>
    
    <h2>Résultats Détaillés</h2>
    
    {% for prompt_eval in prompt_evaluations %}
    <div class="prompt-card">
        <h3>Prompt {{ prompt_eval.prompt_number }}</h3>
        <div class="prompt-text">{{ prompt_eval.prompt }}</div>
        
        <table class="criteria-table">
            <thead>
                <tr>
                    <th>Modèle</th>
                    {% for criterion in criteria %}
                    <th>{{ criterion }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for model_eval in prompt_eval.model_evaluations %}
                <tr>
                    <td>{{ model_eval.model }}</td>
                    {% for criterion in criteria %}
                    <td>
                        {% if model_eval.criteria[criterion] %}
                        <span class="checkmark">✓</span>
                        {% else %}
                        <span class="xmark">✗</span>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        {% for model_eval in prompt_eval.model_evaluations %}
        <div class="model-evaluation">
            <h4>{{ model_eval.model }}</h4>
            
            {% if model_eval.explanation %}
            <div class="explanation">
                <strong>Explication:</strong> {{ model_eval.explanation }}
            </div>
            {% endif %}
            
            {% if model_eval.tools_used_fraction %}
            <p><strong>Outils utilisés:</strong> {{ model_eval.tools_used_fraction }}</p>
            {% endif %}
            
            <details>
                <summary>Voir la réponse</summary>
                <pre>{{ model_eval.response }}</pre>
            </details>
            
            {% if model_eval.html_content %}
            <details>
                <summary>Voir le rendu HTML{% if not model_eval.is_complete_html %} (incomplet){% endif %}</summary>
                <div class="html-render">{{ model_eval.html_content | safe }}</div>
                <pre>{{ model_eval.html_content }}</pre>
            </details>
            {% endif %}
            
            {% if model_eval.tool_calls %}
            <details>
                <summary>Voir les appels d'outils ({{ model_eval.tool_calls | length }})</summary>
                {% for tool_call in model_eval.tool_calls %}
                <div class="tool-call">
                    <h5>Outil: {{ tool_call.tool_name }}</h5>
                    <h6>Arguments:</h6>
                    <pre>{{ tool_call.tool_args | tojson(indent=2) }}</pre>
                    
                    {% if tool_call.tool_result %}
                    <h6>Résultat:</h6>
                    <div class="tool-result">
                        <pre>{{ tool_call.tool_result }}</pre>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </details>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endfor %}
</body>
</html>
