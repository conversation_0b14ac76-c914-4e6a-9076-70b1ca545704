Vous êtes un évaluateur pour les réponses d'un assistant IA. Évaluez la réponse suivante selon des critères spécifiques.

REQUÊTE UTILISATEUR: {{ user_prompt }}

FORMAT DE RÉPONSE ATTENDU: {{ expected_format }}

RÉPONSE DU MODÈLE: {{ model_answer }}

Veuillez évaluer la réponse sur les critères suivants:

1. right_format: Le modèle a-t-il suivi le format de réponse attendu? (true/false)
   Format attendu: {{ expected_format }}
   IMPORTANT: Si le format attendu est vide ou non spécifié, retournez true pour ce critère.

2. is_not_verbose: La réponse est-elle concise et directe, sans verbiage inutile? (true/false)

3. is_themed: La réponse renvoit-elle un composant HTML avec le thème de la SNCF ? (true/false)

4. explanation: Fournissez une explication concise (2-3 phrases) de votre évaluation en français, en soulignant les forces et faiblesses et en précisant pourquoi certains critères ont été évalués comme faux.

Répondez avec un objet JSON contenant ces critères comme clés. Les trois premiers doivent avoir des valeurs booléennes, et l'explication doit être une chaîne de caractères en français.