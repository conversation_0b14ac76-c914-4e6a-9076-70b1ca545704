"""
Utility module for computing performance metrics of LLM models based on tool usage.
This module provides functions to evaluate if the tools called by each model match
the expected tools and if the arguments provided are correct.
"""

from typing import Dict, List, Any, Optional
import datetime


def compute_models_performance(outputs: List[Dict[str, Any]], models: List[str]) -> Dict[str, Any]:
    """
    Compute performance metrics for each model based on tool usage.
    
    Evaluates if the tools called by each model match the expected tools
    and if the arguments provided are correct.
    
    Args:
        outputs: List of prompt evaluation results from evaluate_all_prompts
        models: List of model names
        
    Returns:
        Dict containing performance metrics for each model
    """
    if not outputs:
        print("No outputs available to compute performance metrics.")
        return None
        
    # Initialize performance metrics
    performance_metrics = {
    }
    total_prompts = len(outputs)

    for model in models:
        prompts_performance = [compute_prompts_performance_by_model(output, model) for output in outputs]
        right_tools_count = sum(1 for prompt in prompts_performance if prompt["right_tools"])
        right_args_count = sum(1 for prompt in prompts_performance if prompt["right_args"])
        overall_success_count = sum(1 for prompt in prompts_performance if prompt["right_tools"] and prompt["right_args"])
    
        performance_metrics[model] = {
            "overall": {
                "success_count": overall_success_count,
                "total_prompts": total_prompts,
                "overall_percentage": overall_success_count / total_prompts * 100
            },
            "right_tools": {
                "true": right_tools_count,
                "success_rate": right_tools_count / total_prompts * 100
            },
            "right_args": {
                "true": right_args_count,
                "success_rate": right_args_count / total_prompts * 100
            },
            "prompts_performance": prompts_performance
        }
    return performance_metrics


def compute_prompts_performance_by_model(output: List[Dict[str, Any]], model: str) -> Dict[str, Any]:
    prompts_performance ={
                    "prompt": output["prompt"],
                    "expected_tools": output["expected_tools"],
                    "actual_tool_calls": output["model_evaluations"][model]["tool_calls"],
                    "right_tools": check_right_tools(output["expected_tools"], output["model_evaluations"][model]["tool_calls"]),
                    "right_args": check_right_args(output["expected_tools"], output["model_evaluations"][model]["tool_calls"]),
                    }
    return prompts_performance


def check_right_tools(expected_tools: List[Dict[str, Any]], actual_tool_calls: List[Dict[str, Any]]) -> bool:
    """
    Check if the expected tools were used.
    
    Args:
        expected_tools: List of expected tools
        actual_tool_calls: List of actual tool calls
        
    Returns:
        Boolean indicating if the right tools were used
    """
    # If no tools were expected, check if no tools were used
    if not expected_tools:
        return len(actual_tool_calls) == 0
    
    if len(actual_tool_calls) > len(expected_tools):
        return False
        
    # Else check if all expected tools were used
    expected_tool_names = [tool.get("tool_name") for tool in expected_tools]
    actual_tool_names = [tool.get("tool_name") for tool in actual_tool_calls]
    all_expected_tools_used = all(name in actual_tool_names for name in expected_tool_names)
    
    return all_expected_tools_used



def check_right_args(expected_tools: List[Dict[str, Any]], actual_tool_calls: List[Dict[str, Any]]) -> bool:
    """
    Check if the expected arguments were used for each tool.
    
    Verifies that the expected arguments are included in the actual tool calls,
    without requiring an exact match (additional arguments are allowed).
    
    Args:
        expected_tools: List of expected tools
        actual_tool_calls: List of actual tool calls
        
    Returns: 
        Boolean indicating if the expected arguments were used
    """

    # If no tools were expected, check if no tools were used
    if not expected_tools:
        return True
    
    # Create a dictionary of actual tool calls by name for easier lookup
    expected_tools_args_by_name = {tool.get("tool_name"): tool.get("tool_args") or None for tool in expected_tools}
    for tool_call in actual_tool_calls:
        tool_name = tool_call.get("tool_name")
        if tool_name in expected_tools_args_by_name.keys() and expected_tools_args_by_name[tool_name]:
            expected_args = expected_tools_args_by_name[tool_name]
            actual_args = tool_call.get("tool_args", {})
            # Check if datetime is on current year
            if "datetime" in actual_args:
                if not actual_args["datetime"].startswith(str(datetime.datetime.now().year)):
                    return False
            # Check if all expected arguments are present with correct values
            # Only check that expected args are included (additional args are allowed)
            if all(key in actual_args and actual_args[key] == value for key, value in expected_args.items()):
                return True 
    return False

