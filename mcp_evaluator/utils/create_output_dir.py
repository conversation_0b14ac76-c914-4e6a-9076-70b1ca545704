""" 
Create output directory with the current timestamp into given output folder.
"""

import os
import datetime

from core_client.utils.logger import get_logger



def create_output_directory(output_folder: str) -> str:
    """
    Create output directory with the current timestamp into given output folder.
    
    Args:
        output_folder: Path to the output folder
        
    Returns:
        Path to the created output directory
    """
    # Get a logger for a specific module
    logger = get_logger(__name__)
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(output_folder, f"output_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"Created output directory: {output_dir}")
    return output_dir


if __name__ == "__main__":
    output_dir = create_output_directory("./data/outputs")