"""
Config loader for MCP Evaluator from YML file.
"""

import yaml
from typing import Dict, Any
from pathlib import Path


class Config:
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.inputs_file = None
        self.outputs_folder = None
        self.models = None
        self.quality_evaluator_model = None
        self.quality_mode = None
        self.reproducibility_mode = None
        self.set_config()
        
    def set_config(self):
        with open(self.config_path, "r") as f:
            json_config = yaml.safe_load(f)
        self.inputs_file = json_config.get("inputs_file", "data/inputs.json")
        self.outputs_folder = json_config.get("outputs_folder", "data/outputs")
        self.models = json_config.get("models", ["openai:gpt-4o"])
        self.quality_evaluator_model = json_config.get("quality_evaluator_model", "openai:gpt-4o")
        self.quality_mode = json_config.get("quality_mode", False)
        self.reproducibility_mode = json_config.get("reproducibility_mode", False)

    def __repr__(self):
        return f""" 
        - inputs_file : {self.inputs_file},
        - outputs_folder : {self.outputs_folder},
        - models : 
            - {"\n            - ".join([f"{model}" for model in self.models])}
        - quality_evaluator_model : {self.quality_evaluator_model}
        - quality_mode : {self.quality_mode}
        - reproducibility_mode : {self.reproducibility_mode})
        """
    

def load_config(config_path: str) -> Config:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Config object
    """
    config = Config(config_path)
    print(f"Config loaded : \n {config}")
    return config
   

if __name__ == "__main__":
    config_path = str(Path(__file__).parent.parent / "config.yml")
    config = load_config(config_path)
    print(config)
