"""
Utility module to load JSON inputs to dictionnary format
"""

import json
from typing import Dict, Any, List

def load_inputs(inputs_path: str) -> List[Dict[str, Any]]:
    """
    Load prompts from a JSON file.
             
    Returns: List of prompt data
    """
    try:
        print(f"Loading inputs from {inputs_path}")
        with open(inputs_path, 'r', encoding='utf-8') as f:
            inputs = json.load(f)
        print(f"Loaded {len(inputs)} prompts from {inputs_path}")
        return inputs
    except Exception as e:
        print(f"Error loading input file: {str(e)}")
        raise