# Serveur MCP SNCF

Une implémentation de serveur MCP pour accéder aux données de trains SNCF via les APIs [SNCF](https://doc.navitia.io/) et [SNCF OpenData](https://data.sncf.com/).

## Vue d'ensemble

Ce serveur MCP fournit des outils permettant aux LLMs d'accéder aux informations de trains SNCF en temps réel, incluant :

- Recherche et autocomplétion de gares
- Horaires de départs et d'arrivées
- Planification d'itinéraires avec informations tarifaires
- Recherche d'informations de trains
- Informations de perturbations en temps réel

## Prérequis

- Python 3.12 ou supérieur
- `uv` (gestionnaire de paquets Python) **OU** `pip` (gestionnaire Python standard)
- Clé API SNCF
- Clé API SNCF OpenData

## Installation

```bash
# Via curl
curl -LsSf https://astral.sh/uv/install.sh | sh

# Ou via pip
pip install uv
```

## Configuration

1. Configurer les variables d'environnement :
   - <PERSON>pier `.env.example` vers `.env`
   - Remplir les clés API requises dans le fichier `.env` :
     - `SNCF_API_KEY`: Clé API SNCF
     - `SNCF_BASE_URL`: URL de base de l'API SNCF (par défaut: https://api.sncf.com/v1)
     - `SNCF_OPENDATA_API_KEY`: Clé API SNCF OpenData
     - `SNCF_OPENDATA_BASE_URL`: URL de base de l'API SNCF OpenData (par défaut: https://datasncf.opendatasoft.com/api/explore/v2.1)

## Utilisation

### Configuration locale (stdio)

Le serveur MCP est automatiquement configuré pour fonctionner avec le client Multi-LLM. La configuration par défaut se trouve dans le fichier `config.json` dans le répertoire `core_client/src/core_client`.

## Outils disponibles

Le serveur MCP fournit les outils suivants :

### 1. `autocomplete_places`

Recherche et autocomplétion de gares et d'arrêts.

**Paramètres :**

- `query`: Texte de recherche (requis)
- `searched_type`: Types d'objets à rechercher (par défaut: "stop_area")

**Exemple :**

```json
{
  "query": "Paris",
  "searched_type": "stop_area"
}
```

### 2. `get_departures`

Obtenir les départs d'une gare et toutes les perturbations qui peuvent les affecter.

**Paramètres :**

- `station`: Code ou nom de la gare
- `datetime`: Heure de départ optionnelle (format: YYYYMMDDThhmmss)
- `data_freshness`: Fraîcheur des données (par défaut: "realtime")

**Note :** Le paramètre `count` est fixé à 5 dans l'implémentation actuelle pour éviter d'avoir trop de tokens à traiter pour le LLM.

**Exemple :**

```json
{
  "station": "stop_area:OCE:SA:87391003",
  "datetime": "20241201T080000",
  "data_freshness": "realtime"
}
```

### 3. `get_arrivals`

Obtenir les arrivées à une gare et toutes les perturbations qui peuvent les affecter.

**Paramètres :**

- `station`: Code ou nom de la gare
- `datetime`: Heure d'arrivée optionnelle (format: YYYYMMDDThhmmss)
- `data_freshness`: Fraîcheur des données (par défaut: "realtime")

**Note :** Le paramètre `count` est fixé à 5 dans l'implémentation actuelle pour éviter d'avoir trop de tokens à traiter pour le LLM.

**Exemple :**

```json
{
  "station": "stop_area:OCE:SA:87391003",
  "datetime": "20241201T080000",
  "data_freshness": "realtime"
}
```

### 4. `get_journeys`

Obtenir les itinéraires entre deux gares avec informations tarifaires.

**Paramètres :**

- `from_station`: Code ou nom de la gare de départ (requis)
- `to_station`: Code ou nom de la gare d'arrivée (requis)
- `datetime`: Heure de départ optionnelle (format: YYYYMMDDThhmmss)
- `classe`: Classe de voyage (1 ou 2, par défaut: 2)
- `data_freshness`: Fraîcheur des données (par défaut: "realtime")
- `traveler_type`: Type de voyageur (par défaut: "standard")
- `direct_path`: Préférence de trajet direct (par défaut: "indifferent")

**Note :** Le paramètre `count` est fixé à 5 dans l'implémentation actuelle pour éviter d'avoir trop de tokens à traiter pour le LLM.

**Exemple :**

```json
{
  "from_station": "stop_area:OCE:SA:87391003",
  "to_station": "stop_area:OCE:SA:87722025",
  "classe": 2,
  "datetime": "20241201T080000"
}
```

### 5. `get_train_infos`

Obtenir des informations sur un train spécifique.

**Paramètres :**

- `train_number`: Numéro du train (requis)
- `data_freshness`: Fraîcheur des données (par défaut: "realtime")
- `since`: Heure de début optionnelle (format: YYYYMMDDThhmmss)
- `until`: Heure de fin optionnelle (format: YYYYMMDDThhmmss)

**Note :** Le paramètre `count` est fixé à 2 dans l'implémentation actuelle.

**Exemple :**

```json
{
  "train_number": "6123",
  "data_freshness": "realtime",
  "since": "20241201T060000",
  "until": "20241201T180000"
}
```

## Développement

Le projet utilise :

- `mcp` pour la communication serveur stdio
- `httpx` pour les requêtes HTTP
- `dotenv` pour la gestion des variables d'environnement

### Architecture

Le serveur est structuré comme suit :

- `main.py`: Implémentation principale du serveur avec définitions des outils
- `tools/`: Implémentations des outils pour chaque fonction d'accès aux données SNCF
- `utils/`: Modules utilitaires pour les fonctionnalités communes

### Journalisation (Logging)

Le serveur utilise un système de journalisation centralisé qui supporte différents niveaux de log et écrit à la fois sur la console et dans des fichiers.

#### Niveaux de log

- `DEBUG`: Informations détaillées, généralement utiles uniquement pour diagnostiquer des problèmes
- `INFO`: Confirmation que les choses fonctionnent comme prévu
- `WARNING`: Indication que quelque chose d'inattendu s'est produit, mais l'application fonctionne toujours
- `ERROR`: En raison d'un problème plus grave, l'application n'a pas pu exécuter une fonction
- `CRITICAL`: Une erreur grave, indiquant que l'application elle-même pourrait ne pas pouvoir continuer à fonctionner

#### Configuration

La journalisation peut être configurée via les variables d'environnement dans le fichier `.env` :

```bash
# Configuration de la journalisation
MCP_LOG_LEVEL=INFO          # Niveau de log global (DEBUG, INFO, WARNING, ERROR, CRITICAL)
MCP_CONSOLE_LOG_LEVEL=INFO  # Niveau de log console
MCP_FILE_LOG_LEVEL=DEBUG    # Niveau de log fichier
MCP_LOG_FILE=logs/custom_log_file.log  # Chemin de fichier de log personnalisé (optionnel)
```

Par défaut, les logs sont écrits dans `logs/mcp_server_YYYYMMDD_HHMMSS.log` avec rotation automatique quand les fichiers atteignent 10MB.

#### Utilisation du logger

Pour utiliser le logger dans votre code :

```python
from utils.logger import get_logger

# Obtenir un logger pour votre module
logger = get_logger(__name__)

# Utiliser le logger
logger.debug("Message de débogage")
logger.info("Message d'information")
logger.warning("Message d'avertissement")
logger.error("Message d'erreur")
logger.critical("Message critique")
```

## Dépannage

### Problèmes courants

- **Problèmes de clés API**: Assurez-vous que vos clés API SNCF et SNCF OpenData sont correctement définies
- **Connexion réseau**: Vérifiez votre connexion internet et l'accessibilité des APIs
- **Format des données**: Vérifiez que les paramètres de date/heure sont au bon format (YYYYMMDDThhmmss)
- **Logs**: Consultez les fichiers de log dans le dossier `logs/` pour des informations détaillées sur les erreurs
