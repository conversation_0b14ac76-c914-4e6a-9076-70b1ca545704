<style>
  /* Styles for train schedule table - SNCF style */
  .train-schedule-container {
    font-family: Avenir, Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: transparent;
    color: white;
  }

  .train-schedule-container table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: rgba(162, 162, 162, 0.3);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(95, 95, 95, 0.05);
  }

  .train-schedule-container thead {
    background-color: rgba(0, 136, 206, 0.9); /* SNCF blue */
    color: white;
  }

  .train-schedule-container th {
    padding: 15px 12px;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
  }

  .train-schedule-container td {
    padding: 20px 12px;
    border-bottom: 1px solid rgba(240, 240, 240, 0.5);
    font-size: 16px;
    background-color: transparent;
  }

  .train-schedule-container tr:hover {
    background-color: rgba(0, 136, 206, 0.2);
  }

  .train-schedule-container tr:last-child td {
    border-bottom: none;
  }

  .journey-row {
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .journey-time {
    font-size: 20px;
    font-weight: 700;
    color: white;
  }

  .journey-station {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    margin-top: 4px;
  }

  .departure {
    color: #0088ce; /* SNCF blue */
    font-weight: bold;
  }

  .arrival {
    color: #0088ce; /* SNCF blue */
    font-weight: bold;
  }

  .duration {
    text-align: center;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    width: 100px;
  }

  .train-logo {
    height: 40px;
    width: 100%;
    max-width: 100px;
    display: block;
    margin: 0 auto;
    object-fit: contain;
  }

  /* SVG specific styles */
  .train-logo svg {
    transform: scale(1);
  }

  /* Center content in the carrier column */
  .train-schedule-container td.carrier-column {
    text-align: center;
    vertical-align: middle;
    height: 60px;
    padding: 8px;
    width: 90px;
  }

  /* Correspondence column */
  .train-schedule-container td.correspondence-column {
    text-align: center;
    vertical-align: middle;
    width: 80px;
  }

  /* Price column styling */
  .price-column {
    text-align: right;
    font-weight: 700;
    color: #0088ce;
    width: 100px;
  }

  .price-range {
    font-size: 18px;
    color: #0088ce;
  }

  .price-from {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.85);
    display: block;
    margin-bottom: 4px;
  }

  /* Direct badge */
  .direct-badge {
    display: block;
    background-color: #e6f5fc;
    color: #0088ce;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin: 0 auto;
    text-align: center;
    width: 80%;
  }

  /* Connection badge */
  .connection-badge {
    display: block;
    background-color: #e6f5fc;
    color: #0088ce;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin: 0 auto;
    text-align: center;
    width: 80%;
  }

  /* Journey header */
  .journey-header {
    margin-bottom: 20px;
  }

  .journey-header h2 {
    color: #0088ce;
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 8px 0;
  }

  .journey-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
  }
</style>

<div class="train-schedule-container">
  <div class="journey-header">
    <h2>Trajets disponibles</h2>
    <div class="journey-subtitle">Paris - Marseille</div>
  </div>
  <table>
    <thead>
      <tr>
        <th>Départ</th>
        <th>Arrivée</th>
        <th>Durée</th>
        <th>Corresp.</th>
        <th>Service</th>
        <th>Prix</th>
      </tr>
    </thead>
    <tbody>
      </tr>
    </tbody>
  </table>
</div>