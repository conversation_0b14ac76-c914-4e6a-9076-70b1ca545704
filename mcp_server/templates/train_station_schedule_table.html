<style>
  /* Styles for train station schedule table - SNCF style with configurable colors */
  .train-station-schedule-container {
    font-family: Avenir, Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: transparent;
    color: white;
  }

  .train-station-schedule-container table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: rgba(162, 162, 162, 0.3);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(95, 95, 95, 0.05);
  }

  .train-station-schedule-container thead {
    background-color: var(--header-color); /* Dynamic header color */
    color: white;
  }

  .train-station-schedule-container th {
    padding: 15px 12px;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
  }

  /* Align time and destination columns to the left */
  .train-station-schedule-container th:nth-child(3),
  .train-station-schedule-container th:nth-child(4) {
    text-align: left;
  }

  .train-station-schedule-container td {
    padding: 15px 12px;
    border-bottom: none;
    font-size: 16px;
    background-color: transparent;
  }

  /* Align time and destination columns to the left */
  .train-station-schedule-container td:nth-child(3),
  .train-station-schedule-container td:nth-child(4) {
    text-align: left;
  }

  .train-station-schedule-container tr:hover {
    background-color: var(--hover-color) !important; /* Dynamic hover color */
  }

  .train-station-schedule-container tr:last-child td {
    border-bottom: none;
  }

  /* Alternating row colors inspired by SNCF displays */
  .schedule-row {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .schedule-row:nth-child(odd) {
    background-color: var(--row-odd-color); /* Dynamic odd row color */
  }

  .schedule-row:nth-child(even) {
    background-color: var(--row-even-color); /* Dynamic even row color */
  }

  .schedule-time {
    font-size: 22px;
    font-weight: 700;
    color: #FFD700; /* Yellow color for times - better visibility on colored backgrounds */
  }

  .schedule-station {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 4px;
  }

  .destination-origin {
    color: white; /* White color for destinations/origins on colored background */
    font-weight: bold;
    font-size: 18px;
  }

  .train-number {
    text-align: center;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    width: 100px;
  }

  .train-logo {
    height: 40px;
    width: 100%;
    max-width: 100px;
    display: block;
    margin: 0 auto;
    object-fit: contain;
  }

  /* SVG specific styles */
  .train-logo svg {
    transform: scale(1);
  }

  /* Center content in the carrier column */
  .train-station-schedule-container td.carrier-column {
    text-align: center;
    vertical-align: middle;
    height: 60px;
    padding: 8px;
    width: 90px;
  }

  .schedule-header {
    margin-bottom: 20px;
    text-align: left;
  }

  .schedule-header h2 {
    color: white;
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 8px 0;
  }

  .schedule-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
  }

  /* Color schemes for departures (blue) */
  .departures-theme {
    --header-color: rgba(25, 118, 210, 0.9);
    --hover-color: rgba(25, 118, 210, 0.3);
    --row-odd-color: rgba(25, 118, 210, 0.8);
    --row-even-color: rgba(25, 118, 210, 0.6);
  }

  /* Color schemes for arrivals (green) */
  .arrivals-theme {
    --header-color: rgba(34, 139, 34, 0.9);
    --hover-color: rgba(34, 139, 34, 0.3);
    --row-odd-color: rgba(34, 139, 34, 0.8);
    --row-even-color: rgba(34, 139, 34, 0.6);
  }
</style>

<div class="train-station-schedule-container SCHEDULE_THEME_CLASS">
  <div class="schedule-header">
    <h2>SCHEDULE_TITLE</h2>
    <div class="schedule-subtitle">STATION_NAME</div>
  </div>
  <table>
    <thead>
      <tr>
        <th>Service</th>
        <th>N°</th>
        <th>Heure</th>
        <th>DESTINATION_ORIGIN_LABEL</th>
      </tr>
    </thead>
    <tbody>
      </tr>
    </tbody>
  </table>
</div>
