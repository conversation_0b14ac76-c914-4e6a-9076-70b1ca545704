"""
Tool for getting journey information between two stations.
"""

from typing import Any, Optional, Dict
from mcp.types import Tool

from tools import SncfTools
from tools.autocomplete_places import autocomplete_places

from utils.sncf_api import make_sncf_api_request
from utils.opendata import get_prices_from_opendata

async def get_journeys(
    from_station: str,
    to_station: str,
    datetime: str | None = None,
    # count: int = 5,
    classe: int = 2,
    data_freshness: str = "realtime",
    traveler_type: str = "standard",
    direct_path: str = "indifferent",
) -> Any:
    """
    Get journeys between two stations, and the disruptions that may affect them.

    Args:
        from_station: Departure station name or code
        to_station: Arrival station name or code
        datetime: Optional departure time (format: YYYYMMDDThhmmss)
        count: Number of journeys to return
        classe: Travel class (1 or 2)
        data_freshness: Data freshness level (realtime, base_schedule, adapted_schedule)
        traveler_type: Type of traveler (standard, slow_walker, fast_walker, wheelchair, luggage)
        direct_path: Direct path preference (indifferent, none, only, only_with_alternatives)

    Returns:
        Dictionary with journeys and disruptions
    """
    count = 5
    # Check if already stop_area or stop_point
    if from_station.startswith("stop_area:") or from_station.startswith("stop_point:"):
        from_place = from_station
    else:
        from_place = await autocomplete_places(query=from_station, searched_type="stop_area", count=1)
        if from_place is None:
            return "Error: No from place found"
        from_place = from_place[0]["id"]

    if to_station.startswith("stop_area:") or to_station.startswith("stop_point:"):
        to_place = to_station
    else:
        
        to_place = await autocomplete_places(query=to_station, searched_type="stop_area", count=1)
        if to_place is None:
            return "Error: No to place found"
        to_place = to_place[0]["id"]

    journeys = await make_sncf_api_request(f"journeys?from={from_place}&to={to_place}&count={count}{'&datetime=' + datetime if datetime else ''}&data_freshness={data_freshness}&traveler_type={traveler_type}&direct_path={direct_path}&forbidden_uris%5B%5D=physical_mode%3ARapidTransit&forbidden_uris%5B%5D=physical_mode%3ABus&forbidden_uris%5B%5D=physical_mode%3ACoach&forbidden_uris%5B%5D=physical_mode%3ATramway")
    if journeys is None:
        return "Error: No journeys found"

    # Filter and simplify the journeys for LLM consumption
    result = {
        "journeys": [],
        "disruptions": []
    }

    for journey in journeys["journeys"]:
        simplified_journey = {
            "departure": journey["departure_date_time"],
            "arrival": journey["arrival_date_time"],
            "duration": journey["duration"],  # Duration in seconds
            "nb_transfers": journey.get("nb_transfers", 0),
            "type": journey.get("type", ""),
            "sections": []
        }

        # Process each section of the journey
        for section in journey["sections"]:
            simplified_section = {
                "from": section["from"]["name"] if "from" in section else "",
                "to": section["to"]["name"] if "to" in section else "",
                "departure_time": section.get("departure_date_time", ""),
                "arrival_time": section.get("arrival_date_time", "")
            }

            # Add mode-specific information
            if section["type"] == "public_transport":
                if "display_informations" in section:
                    info = section["display_informations"]
                    simplified_section["transport_info"] = {
                        "network": info.get("network", ""),
                        "code": info.get("code", ""),
                        "trip_short_name": info.get("trip_short_name", ""),
                        "direction": info.get("direction", ""),
                        "physical_mode": info.get("physical_mode", ""),
                        "prices": await get_prices_from_opendata(departure = from_place, arrival = to_place, classe = classe) or ""
                    }
                    
                if "route" in section:
                    simplified_section["route"] = section["route"].get("direction", "")

                simplified_journey["sections"].append(simplified_section)

        # Add the journey to the result after processing all its sections
        result["journeys"].append(simplified_journey)

    # Process disruptions after all journeys have been processed
    for disruption in journeys.get("disruptions", []):
        try:
            simplified_disruption = {
                "id": disruption.get("id", "unknown"),
                "application_periods": disruption.get("application_periods", [])
            }

            # Safely access impacted_objects and pt_object
            if "impacted_objects" in disruption:
                impacted_objects = disruption["impacted_objects"]
                if isinstance(impacted_objects, dict) and "pt_object" in impacted_objects:
                    simplified_disruption["impacted_objects"] = impacted_objects["pt_object"]
                else:
                    simplified_disruption["impacted_objects"] = impacted_objects
            else:
                simplified_disruption["impacted_objects"] = []

            # Safely access messages
            if "messages" in disruption:
                messages = disruption["messages"]
                if isinstance(messages, dict) and "text" in messages:
                    simplified_disruption["messages"] = messages["text"]
                else:
                    simplified_disruption["messages"] = str(messages)

            result["disruptions"].append(simplified_disruption)
        except Exception as e:
            print(f"Error processing disruption: {e}")

    return result


def get_tool_definition() -> Tool:
    """
    Get the Tool definition for the get_journeys tool.

    Returns:
        Tool: The Tool definition
    """
    return Tool(
        name=SncfTools.GET_JOURNEYS.value,
        description="""
            Get journeys between two stations with prices and disruptions information (if adapted_schedule is used).
            
            **WARNING**: if the user tells you a city with multiple stations, you should guess the right station by yourself and not use the city name as a station name. If you don't know the station name, you should use the autocomplete_places tool to get the right station.
            **WARNING**: You should always consider the current date and time when using the datetime parameter. You should avoid using a datetime in the past, for instance, using the wrong year.
            
            When this tool is used, a summary html table will be generated and displayed to the user, containing trains number, company, departure and arrival time and stations, duration and price.
            It also returns to you the raw data used to generate this table, in order for you to get more details, with for instance stops, prices for all types of clients, etc...
            """,
        inputSchema={
            "type": "object",
            "properties": {
                "from_station": {
                    "type": "string",
                    "description": "Guessed EXACT STATION name according to the user's input, or station code (stop_area:code or stop_point:code) after using autocomplete_places tool. Be carefull : If multiple stations are possible (for example, Paris can be Paris Bercy, Paris Est, Paris Gare de Lyon, etc...), you should guess the right one by yourself and not use Paris as a station name. If you don't know the station name, you should use the autocomplete_places tool to get the station code, but avoid using it when possible.",
                },
                "to_station": {
                    "type": "string",
                    "description": "Guessed EXACT STATION name according to the user's input, or station code (stop_area:code or stop_point:code) after using autocomplete_places tool. Be carefull : If multiple stations are possible (for example, Paris can be Paris Bercy, Paris Est, Paris Gare de Lyon, etc...), you should guess the right one by yourself and not use Paris as a station name. If you don't know the station name, you should use the autocomplete_places tool to get the station code, but avoid using it when possible.",
                },
                "datetime": {
                    "type": "string",
                    "description": "Optional departure time (format: YYYYMMDDThhmmss). By default, it uses the current time. When used, be careful to use a time in the future, considering the current time, date and year.",
                    "default": None
                },
                "classe": {
                    "type": "integer",
                    "description": "Class of the journey (1, 2)",
                    "default": 2
                },
                "data_freshness": {
                    "type": "string",
                    "description": "Data freshness (realtime, base_schedule or adapted_schedule). Adapted schedule to get disruptions information.",
                    "default": "realtime"
                },
                "traveler_type": {
                    "type": "string",
                    "description": "Traveler type (standard, slow_walker, fast_walker, wheelchair, luggage)",
                    "default": "standard"
                },
                "direct_path": {
                    "type": "string",
                    "description": "Flag (indifferent, none, only, only_with_alternatives) to indicate if direct paths are preferred",
                    "default": "indifferent"
                },
            },
            "required": ["from_station", "to_station"]
        },
    )