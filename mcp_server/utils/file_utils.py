"""
Utility functions for file operations.
"""

from utils.logger import get_logger

# Get logger for this module
logger = get_logger(__name__)

def read_file_content(filepath: str) -> str:
    """
    Read content from a file with proper error handling.

    Args:
        filepath: Path to the file to read

    Returns:
        File content as string or empty string if file not found
    """
    try:
        logger.debug(f"Reading file: {filepath}")
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            logger.debug(f"Successfully read {len(content)} bytes from {filepath}")
            return content
    except FileNotFoundError:
        logger.error(f"File not found at {filepath}")
        return ""
    except Exception as e:
        logger.error(f"Error reading file {filepath}: {e}", exc_info=True)
        return ""
