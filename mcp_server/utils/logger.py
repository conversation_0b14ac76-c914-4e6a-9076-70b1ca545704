"""
Centralized logging configuration for the MCP server.

This module provides a consistent logging interface for all components of the MCP server.
It supports different log levels, file and console logging, and log rotation.

Usage:
    from utils.logger import get_logger

    # Get a logger for a specific module
    logger = get_logger(__name__)
    
    # Use the logger
    logger.debug("Debug message")
    logger.info("Info message")
    logger.warning("Warning message")
    logger.error("Error message")
    logger.critical("Critical message")
"""

import logging
import os
import sys
from logging.handlers import RotatingFileHandler
from datetime import datetime
from typing import Optional, Dict, Any

# Default log directory
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")

# Create logs directory if it doesn't exist
os.makedirs(LOG_DIR, exist_ok=True)

# Default log filename with timestamp
DEFAULT_LOG_FILENAME = f"mcp_server_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# Default log format
DEFAULT_LOG_FORMAT = '%(asctime)s [%(levelname)s] [%(name)s:%(funcName)s:%(lineno)d] %(message)s'

# Default date format for logs
DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Map string log levels to logging module constants
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL,
}

# Global dictionary to store loggers
_loggers: Dict[str, logging.Logger] = {}

def configure_logging(
    log_level: str = None,
    log_file: str = None,
    console_log_level: str = None,
    file_log_level: str = None,
    log_format: str = DEFAULT_LOG_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT,
    max_file_size_mb: int = 10,
    backup_count: int = 5,
) -> None:
    """
    Configure the root logger with file and console handlers.
    
    Args:
        log_level: Overall log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (default: logs/mcp_server_YYYYMMDD_HHMMSS.log)
        console_log_level: Log level for console output (default: same as log_level)
        file_log_level: Log level for file output (default: same as log_level)
        log_format: Format string for log messages
        date_format: Format string for log timestamps
        max_file_size_mb: Maximum size of log file in MB before rotation
        backup_count: Number of backup log files to keep
    """
    # Get log level from environment variable if not provided
    if log_level is None:
        log_level = os.getenv('MCP_LOG_LEVEL', 'INFO').upper()
    
    # Convert string log level to logging module constant
    log_level_value = LOG_LEVELS.get(log_level.upper(), logging.INFO)
    
    # Set console log level (default to same as overall log level)
    if console_log_level is None:
        console_log_level = os.getenv('MCP_CONSOLE_LOG_LEVEL', log_level).upper()
    console_log_level_value = LOG_LEVELS.get(console_log_level.upper(), log_level_value)
    
    # Set file log level (default to same as overall log level)
    if file_log_level is None:
        file_log_level = os.getenv('MCP_FILE_LOG_LEVEL', log_level).upper()
    file_log_level_value = LOG_LEVELS.get(file_log_level.upper(), log_level_value)
    
    # Set log file path (default to logs/mcp_server_YYYYMMDD_HHMMSS.log)
    if log_file is None:
        log_file = os.getenv('MCP_LOG_FILE', os.path.join(LOG_DIR, DEFAULT_LOG_FILENAME))
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level_value)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(log_format, date_format)
    
    # Set up console handler
    console_handler = logging.StreamHandler(sys.stderr)
    console_handler.setLevel(console_log_level_value)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Set up file handler with rotation
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=max_file_size_mb * 1024 * 1024,
        backupCount=backup_count
    )
    file_handler.setLevel(file_log_level_value)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Log configuration details
    root_logger.info(f"Logging configured: level={log_level}, file={log_file}")
    root_logger.debug(f"Console log level: {console_log_level}, File log level: {file_log_level}")

def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Name of the logger (typically __name__)
        
    Returns:
        Logger instance
    """
    if name not in _loggers:
        logger = logging.getLogger(name)
        _loggers[name] = logger
    
    return _loggers[name]
